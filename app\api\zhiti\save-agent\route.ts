import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { v4 as uuidv4 } from "uuid";
export const runtime = "edge";

export async function POST(req: NextRequest) {
  try {
    const { 
      agentName, 
      description, 
      targetAudience, 
      features, 
      faq, 
      systemPrompt, 
      imageUrl, 
      modelId 
    } = await req.json();
    
    if (!agentName || !description || !systemPrompt) {
      return NextResponse.json(
        { error: "必须提供智能体名称、描述和系统提示词" },
        { status: 400 }
      );
    }
    
    // 生成唯一标识符/URL slug
    const slug = uuidv4().substring(0, 8);
    
    // 保存到Supabase
    const { data, error } = await supabase
      .from('agents')
      .insert([
        { 
          name: agentName,
          slug,
          description,
          target_audience: targetAudience,
          features,
          faq,
          system_prompt: systemPrompt,
          image_url: imageUrl,
          model_id: modelId,
          created_at: new Date().toISOString(),
        }
      ])
      .select();
    
    if (error) {
      console.error("Supabase error:", error);
      return NextResponse.json(
        { error: "保存智能体失败" },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      success: true,
      slug,
      agentId: data[0].id
    });
  } catch (error) {
    console.error("Error saving agent:", error);
    return NextResponse.json(
      { error: "处理请求失败" },
      { status: 500 }
    );
  }
} 