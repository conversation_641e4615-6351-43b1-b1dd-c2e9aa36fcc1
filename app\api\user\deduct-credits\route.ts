import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { getUserUuid } from '@/services/user';
import { decreaseCredits, CreditsTransType } from '@/services/credit';
export const runtime = "edge";

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' }, 
        { status: 401 }
      );
    }
    
    // 获取用户UUID
    const user_uuid = await getUserUuid();
    
    if (!user_uuid) {
      return NextResponse.json(
        { error: 'User UUID not found' }, 
        { status: 404 }
      );
    }
    
    // 从请求体中获取积分消耗数量和交易类型
    const body = await request.json();
    const { amount, trans_type = CreditsTransType.Ping } = body;
    
    if (!amount || typeof amount !== 'number' || amount <= 0) {
      return NextResponse.json(
        { error: 'Invalid credit amount' },
        { status: 400 }
      );
    }
    
    // 使用credit服务中的decreaseCredits函数扣除积分
    await decreaseCredits({
      user_uuid,
      trans_type,
      credits: amount
    });
    
    return NextResponse.json({
      success: true,
      message: `Successfully deducted ${amount} credits`
    });
    
  } catch (error) {
    console.error('Error deducting credits:', error);
    return NextResponse.json(
      { error: 'Failed to deduct credits', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
} 