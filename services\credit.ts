import {
  findCreditByOrderNo,
  getUserValidCredits,
  insertCredit,
} from "@/models/credit";

import { Credit } from "@/types/credit";
import { Order } from "@/types/order";
import { UserCredits } from "@/types/user";
import { findUserByUuid } from "@/models/user";
import { getFirstPaidOrderByUserUuid } from "@/models/order";
import { getIsoTimestr } from "@/lib/time";
import { getSnowId } from "@/lib/hash";
import { supabase } from "@/lib/supabase";

export enum CreditsTransType {
  NewUser = "new_user", // initial credits for new user
  OrderPay = "order_pay", // user pay for credits
  SystemAdd = "system_add", // system add credits
  Ping = "ping", // cost for ping api
  SystemRefund = "system_refund", // system refund credits
  DailyReward = "daily_reward", // daily reward credits
}

export enum CreditsAmount {
  NewUserGet = 10,
  PingCost = 1,
  ImageGenerationCost = 3,
  DailyReward = 10, // 每日赠送积分数量
}

export async function getUserCredits(user_uuid: string): Promise<UserCredits> {
  let user_credits: UserCredits = {
    left_credits: 0,
  };

  try {
    const first_paid_order = await getFirstPaidOrderByUserUuid(user_uuid);
    if (first_paid_order) {
      user_credits.is_recharged = true;
    }

    const credits = await getUserValidCredits(user_uuid);
    if (credits) {
      credits.forEach((v: Credit) => {
        user_credits.left_credits += v.credits;
      });
    }

    if (user_credits.left_credits < 0) {
      user_credits.left_credits = 0;
    }

    if (user_credits.left_credits > 0) {
      user_credits.is_pro = true;
    }

    return user_credits;
  } catch (e) {
    console.log("get user credits failed: ", e);
    return user_credits;
  }
}

export async function decreaseCredits({
  user_uuid,
  trans_type,
  credits,
}: {
  user_uuid: string;
  trans_type: CreditsTransType;
  credits: number;
}) {
  try {
    console.log(`💰 开始扣减积分: ${credits} 积分，用户: ${user_uuid}, 类型: ${trans_type}`);

    // 1. 获取用户所有有效积分记录（按过期时间排序，优先使用快过期的）
    const userCredits = await getUserValidCredits(user_uuid);
    console.log(`📊 用户有效积分记录数量: ${userCredits?.length || 0}`);

    if (!userCredits || userCredits.length === 0) {
      console.log(`❌ 用户没有有效积分`);
      throw new Error(`用户没有有效积分`);
    }

    // 2. 计算总可用积分
    let totalAvailableCredits = 0;
    userCredits.forEach((credit: Credit) => {
      totalAvailableCredits += credit.credits;
    });

    console.log(`📊 用户总可用积分: ${totalAvailableCredits}`);

    // 3. 检查积分是否足够
    if (totalAvailableCredits < credits) {
      console.log(`❌ 积分不足: 需要 ${credits}，可用 ${totalAvailableCredits}`);
      throw new Error(`积分不足，需要 ${credits} 积分，当前可用 ${totalAvailableCredits} 积分`);
    }

    // 4. 按照 FIFO 原则扣减积分（优先扣减快过期的）
    let remainingToDeduct = credits;
    let deductionRecords = [];

    for (let i = 0; i < userCredits.length && remainingToDeduct > 0; i++) {
      const credit = userCredits[i];
      const availableInThisCredit = credit.credits;

      if (availableInThisCredit <= 0) {
        continue; // 跳过已经用完的积分记录
      }

      // 计算从这个积分记录中扣减多少
      const toDeductFromThis = Math.min(remainingToDeduct, availableInThisCredit);

      if (toDeductFromThis > 0) {
        deductionRecords.push({
          order_no: credit.order_no,
          expired_at: credit.expired_at,
          amount: toDeductFromThis
        });

        remainingToDeduct -= toDeductFromThis;
        console.log(`📝 从订单 ${credit.order_no || '免费积分'} 扣减 ${toDeductFromThis} 积分`);
      }
    }

    // 5. 为每个扣减创建积分记录
    for (const deduction of deductionRecords) {
      const new_credit: Credit = {
        trans_no: getSnowId(),
        created_at: getIsoTimestr(),
        user_uuid: user_uuid,
        trans_type: trans_type,
        credits: 0 - deduction.amount, // 负数表示扣减
        order_no: deduction.order_no,
        expired_at: deduction.expired_at || "",
      };

      await insertCredit(new_credit);
      console.log(`✅ 创建扣减记录: ${deduction.amount} 积分，订单: ${deduction.order_no || '免费积分'}`);
    }

    console.log(`✅ 积分扣减完成: 总共扣减 ${credits} 积分，创建了 ${deductionRecords.length} 条记录`);

  } catch (e) {
    console.log("积分扣减失败: ", e);
    throw e;
  }
}

export async function increaseCredits({
  user_uuid,
  trans_type,
  credits,
  expired_at,
  order_no,
}: {
  user_uuid: string;
  trans_type: string;
  credits: number;
  expired_at?: string;
  order_no?: string;
}) {
  try {
    const new_credit: Credit = {
      trans_no: getSnowId(),
      created_at: getIsoTimestr(),
      user_uuid: user_uuid,
      trans_type: trans_type,
      credits: credits,
      order_no: order_no || "",
      expired_at: expired_at || "",
    };
    await insertCredit(new_credit);
  } catch (e) {
    console.log("increase credits failed: ", e);
    throw e;
  }
}

export async function updateCreditForOrder(order: Order) {
  try {
    const credit = await findCreditByOrderNo(order.order_no);
    if (credit) {
      // order already increased credit
      return;
    }

    await increaseCredits({
      user_uuid: order.user_uuid,
      trans_type: CreditsTransType.OrderPay,
      credits: order.credits,
      expired_at: order.expired_at,
      order_no: order.order_no,
    });
  } catch (e) {
    console.log("update credit for order failed: ", e);
    throw e;
  }
}
