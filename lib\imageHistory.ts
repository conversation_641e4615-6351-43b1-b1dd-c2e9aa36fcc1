import { supabase } from './supabase';

export interface ImageHistory {
  id: string;
  prompt: string;
  image_url: string;
  created_at: string;
}

/**
 * Get all images created by a specific user
 * @param userUuid The user's UUID
 * @param limit 限制返回的记录数量，默认20条
 * @returns Array of user's images
 */
export async function getImagesByUser(userUuid: string, limit: number = 20): Promise<ImageHistory[]> {
  try {
    if (!userUuid) {
      return [];
    }
    
    if (!supabase) {
      console.error('Supabase client is not initialized');
      return [];
    }
    
    const { data, error } = await supabase
      .from('images')
      .select('id, prompt, image_url, created_at')
      .eq('user_id', userUuid)
      .order('created_at', { ascending: false })
      .limit(limit);
    
    if (error) {
      console.error('Error fetching user images:', error);
      return [];
    }
    
    return data || [];
  } catch (error) {
    console.error('Error fetching user images:', error);
    return [];
  }
}

/**
 * Get user UUID from session
 * @returns User UUID or empty string
 */
export async function getUserUuid() {
  try {
    const { data } = await supabase.auth.getSession();
    return data?.session?.user?.id || '';
  } catch (error) {
    console.error('Error getting user UUID:', error);
    return '';
  }
}

/**
 * Get user UUID from request (for API routes)
 * @param request NextRequest object
 * @returns User UUID or empty string
 */
export async function getUserUuidFromRequest(request: Request) {
  try {
    const authHeader = request.headers.get('Authorization');
    if (!authHeader) {
      return '';
    }
    
    const token = authHeader.replace('Bearer ', '');
    if (!token) {
      return '';
    }
    
    const { data } = await supabase.auth.getUser(token);
    return data?.user?.id || '';
  } catch (error) {
    console.error('Error getting user UUID from request:', error);
    return '';
  }
} 