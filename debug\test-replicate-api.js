// Test script for Replicate API
// Run with: node debug/test-replicate-api.js

const fetch = require('node-fetch');

async function testReplicateAPI() {
  try {
    console.log('Testing Replicate API...');
    
    const response = await fetch('http://localhost:3000/api/replicate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: 'A beautiful sunset over mountains, photorealistic',
        categoryId: 'test-category',
        prompt_id: `test-${Date.now()}`,
        width: 768,
        height: 1152,
        count: 1,
        aspect_ratio: '3:4',
        style: 'photorealistic'
      })
    });

    console.log('Response status:', response.status);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response:', errorText);
      return;
    }

    const data = await response.json();
    console.log('Success response:', JSON.stringify(data, null, 2));
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the test
testReplicateAPI();
