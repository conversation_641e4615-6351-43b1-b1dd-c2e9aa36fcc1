import { NextResponse } from "next/server";
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { createImage } from "@/models/image";
import { getSupabaseClient } from "@/models/db";
import { withApiLogger } from "@/lib/api-logger";
import { NextRequest } from "next/server";
import { supabase } from '@/lib/supabase'
import { getSignedUrl } from "@aws-sdk/s3-request-presigner"

// For Cloudflare deployment, must use Edge Runtime
export const runtime = "edge";

// Initialize R2 client
const s3Client = new S3Client({
  region: process.env.R2_REGION || "auto",
  endpoint: process.env.R2_ENDPOINT || `https://${process.env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: process.env.R2_ACCESS_KEY_ID || process.env.CLOUDFLARE_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY || process.env.CLOUDFLARE_SECRET_ACCESS_KEY || '',
  },
});

async function handleWebhook(req: Request) {
  try {
    console.log('Received webhook request...');
    const prediction = await req.json();
    console.log('Webhook payload:', prediction);
    
    // Only process completed predictions
    if (prediction.status !== "succeeded") {
      console.log('Ignoring non-succeeded prediction:', prediction.status);
      return NextResponse.json({ status: "ignored" });
    }

    // Extract metadata from prediction
    const { 
      id: predictionId,
      input,
      output,
      metadata 
    } = prediction;

    console.log('Processing prediction:', { predictionId, input, metadata });

    if (!output || !Array.isArray(output)) {
      console.error('Invalid prediction output:', output);
      throw new Error("Invalid prediction output");
    }

    // Get the image URL from output
    const imageUrl = output[0];
    console.log('Generated image URL:', imageUrl);
    
    // Fetch image
    console.log('Fetching image from Replicate...');
    const response = await fetch(imageUrl);
    if (!response.ok) {
      console.error('Failed to fetch image:', response.status, response.statusText);
      throw new Error("Failed to fetch generated image");
    }
    
    const imageBuffer = await response.arrayBuffer();
    console.log('Image fetched successfully, size:', imageBuffer.byteLength);
    
    // Upload to R2
    const fileName = `ai-image-${Date.now()}-${predictionId}.png`;
    console.log('Uploading to R2:', fileName);
    
    try {
      await s3Client.send(
        new PutObjectCommand({
          Bucket: process.env.R2_BUCKET_NAME || process.env.CLOUDFLARE_R2_BUCKET_NAME || '',
          Key: fileName,
          Body: Buffer.from(imageBuffer),
          ContentType: 'image/png',
        })
      );
      console.log('Successfully uploaded to R2');
    } catch (error) {
      console.error('R2 upload failed:', error);
      throw error;
    }
    
    const r2Url = `${process.env.R2_PUBLIC_URL || process.env.NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_DOMAIN}/${fileName}`;
    console.log('R2 public URL:', r2Url);

    // 从数据库获取原始记录
    console.log('Fetching original record from database...');
    const supabase = getSupabaseClient();
    let { data: originalRecord, error: fetchError } = await supabase
      .from('imagesing')
      .select('*')
      .eq('metadata->predictionId', predictionId)
      .single();

    if (fetchError) {
      console.error('Error fetching original record:', fetchError);
      // 尝试使用不同的查询方式
      const { data: altRecord, error: altError } = await supabase
        .from('imagesing')
        .select('*')
        .eq('status', 'processing')
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (altError) {
        console.error('Error fetching alternative record:', altError);
        throw altError;
      }

      if (!altRecord) {
        console.error('No processing record found');
        throw new Error('No processing record found');
      }

      console.log('Found alternative record:', altRecord);
      originalRecord = altRecord;
    }

    if (!originalRecord) {
      console.error('Original record not found for prediction:', predictionId);
      throw new Error('Original record not found');
    }

    console.log('Found original record:', originalRecord);

    try {
      // 首先更新 prompts 表状态
      if (originalRecord.metadata?.prompt_id) {
        console.log('Updating prompt status for prompt_id:', originalRecord.metadata.prompt_id);
        const { error: promptUpdateError } = await supabase
          .from('prompts')
          .update({
            image_status: 'generated',
            image_url: r2Url,
            image_error: null
          })
          .eq('id', originalRecord.metadata.prompt_id);

        if (promptUpdateError) {
          console.error('Error updating prompt:', promptUpdateError);
          throw promptUpdateError;
        }
        console.log('Prompt updated successfully');
      } else {
        console.log('No prompt_id found in metadata, skipping prompt update');
      }

      // 然后更新 imagesing 表
      console.log('Updating collection status...');
      const { error: updateError } = await supabase
        .from('imagesing')
        .update({
          image_url: JSON.stringify([r2Url]),
          status: 'active',
          metadata: {
            ...originalRecord.metadata,
            predictionId,
            originalUrl: imageUrl,
            model: "minimax/image-01",
            allImages: [r2Url],
            allOriginalUrls: [imageUrl],
            images: [r2Url]
          }
        })
        .eq('id', originalRecord.id);

      if (updateError) {
        console.error('Error updating collection:', updateError);
        throw updateError;
      }
      console.log('Collection updated successfully');

      // 最后保存到 images 表
      console.log('Saving image to database...');
      await createImage({
        category_id: originalRecord.category_id,
        prompt: input.prompt,
        image_url: r2Url,
        user_id: originalRecord.user_id,
        status: 'active',
        metadata: {
          ...originalRecord.metadata,
          predictionId,
          originalUrl: imageUrl,
          model: "minimax/image-01"
        }
      });
      console.log('Image saved to database successfully');

    } catch (error) {
      console.error('Database operation failed:', error);
      throw error;
    }

    console.log('Webhook processing completed successfully');
    return NextResponse.json({ 
      status: "success",
      imageUrl: r2Url,
      promptId: originalRecord.metadata?.prompt_id
    });
  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Webhook processing failed" },
      { status: 500 }
    );
  }
}

// Exported POST method, wrapped with withApiLogger
export async function POST(req: NextRequest) {
  try {
    console.log('Webhook received:', new Date().toISOString())
    
    const body = await req.json()
    console.log('Webhook body:', JSON.stringify(body, null, 2))

    const { id: predictionId, status, output, error } = body

    if (!predictionId) {
      console.error('No prediction ID in webhook')
      return NextResponse.json({ error: 'No prediction ID' }, { status: 400 })
    }

    // 1. 查找对应的 prompt
    console.log('Finding prompt with prediction ID:', predictionId)
    const { data: prompt, error: findError } = await supabase
      .from('prompts')
      .select('*')
      .eq('prediction_id', predictionId)
      .single()

    if (findError) {
      console.error('Error finding prompt:', findError)
      return NextResponse.json({ error: 'Prompt not found' }, { status: 404 })
    }

    if (!prompt) {
      console.error('No prompt found for prediction ID:', predictionId)
      return NextResponse.json({ error: 'Prompt not found' }, { status: 404 })
    }

    console.log('Found prompt:', prompt.id)

    // 2. 处理不同状态
    if (status === 'succeeded' && output && output.length > 0) {
      console.log('Processing successful generation')
      
      try {
        // 2.1 获取图片数据
        console.log('Fetching image from:', output[0])
        const imageResponse = await fetch(output[0])
        if (!imageResponse.ok) {
          throw new Error(`Failed to fetch image: ${imageResponse.statusText}`)
        }
        const imageBuffer = await imageResponse.arrayBuffer()
        console.log('Image fetched successfully')

        // 2.2 上传到 R2
        const key = `generated/${prompt.id}/${Date.now()}.png`
        console.log('Uploading to R2 with key:', key)
        
        const command = new PutObjectCommand({
          Bucket: process.env.R2_BUCKET_NAME || process.env.CLOUDFLARE_R2_BUCKET_NAME || '',
          Key: key,
          Body: Buffer.from(imageBuffer),
          ContentType: 'image/png',
        })

        await s3Client.send(command)
        console.log('Image uploaded to R2 successfully')

        // 2.3 生成访问 URL
        const imageUrl = `${process.env.R2_PUBLIC_URL || process.env.NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_DOMAIN}/${key}`
        console.log('Generated image URL:', imageUrl)

        // 2.4 更新数据库
        console.log('Updating database with image URL')
        const { error: updateError } = await supabase
          .from('prompts')
          .update({ 
            image_url: imageUrl,
            image_error: null,
            status: 'completed'
          })
          .eq('id', prompt.id)

        if (updateError) {
          console.error('Error updating database:', updateError)
          throw updateError
        }
        console.log('Database updated successfully')

      } catch (error) {
        console.error('Error processing successful generation:', error)
        // 记录错误到数据库
        await supabase
          .from('prompts')
          .update({ 
            image_error: error instanceof Error ? error.message : 'Failed to process image',
            status: 'failed'
          })
          .eq('id', prompt.id)
        throw error
      }

    } else if (status === 'failed') {
      console.log('Processing failed generation')
      // 记录错误到数据库
      const { error: updateError } = await supabase
        .from('prompts')
        .update({ 
          image_error: error || 'Generation failed',
          status: 'failed'
        })
        .eq('id', prompt.id)

      if (updateError) {
        console.error('Error updating database with failure:', updateError)
        throw updateError
      }
      console.log('Failure recorded in database')
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Webhook processing error:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
} 