import { NextResponse } from "next/server";
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { createImage } from "@/models/image";
import { getSupabaseClient } from "@/models/db";
import { withApiLogger } from "@/lib/api-logger";
import { NextRequest } from "next/server";
import { supabase } from '@/lib/supabase'


// For Cloudflare deployment, must use Edge Runtime
export const runtime = "edge";

// Initialize R2 client
const s3Client = new S3Client({
  region: process.env.R2_REGION || "auto",
  endpoint: process.env.R2_ENDPOINT || `https://${process.env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: process.env.R2_ACCESS_KEY_ID || process.env.CLOUDFLARE_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY || process.env.CLOUDFLARE_SECRET_ACCESS_KEY || '',
  },
});

async function handleWebhook(req: Request) {
  try {
    console.log('Received webhook request...');
    const prediction = await req.json();
    console.log('Webhook payload:', prediction);

    // Only process completed predictions
    if (prediction.status !== "succeeded") {
      console.log('Ignoring non-succeeded prediction:', prediction.status);
      return NextResponse.json({ status: "ignored" });
    }

    // Extract data from prediction
    const {
      id: predictionId,
      input,
      output
    } = prediction;

    console.log('Processing prediction:', { predictionId, input });

    if (!output || !Array.isArray(output)) {
      console.error('Invalid prediction output:', output);
      throw new Error("Invalid prediction output");
    }

    // Get the image URL from output
    const imageUrl = output[0];
    console.log('Generated image URL:', imageUrl);
    
    // Fetch image
    console.log('Fetching image from Replicate...');
    const response = await fetch(imageUrl);
    if (!response.ok) {
      console.error('Failed to fetch image:', response.status, response.statusText);
      throw new Error("Failed to fetch generated image");
    }
    
    const imageBuffer = await response.arrayBuffer();
    console.log('Image fetched successfully, size:', imageBuffer.byteLength);
    
    // Upload to R2
    const fileName = `ai-image-${Date.now()}-${predictionId}.png`;
    console.log('Uploading to R2:', fileName);
    
    try {
      await s3Client.send(
        new PutObjectCommand({
          Bucket: process.env.R2_BUCKET_NAME || process.env.CLOUDFLARE_R2_BUCKET_NAME || '',
          Key: fileName,
          Body: Buffer.from(imageBuffer),
          ContentType: 'image/png',
        })
      );
      console.log('Successfully uploaded to R2');
    } catch (error) {
      console.error('R2 upload failed:', error);
      throw error;
    }
    
    const r2Url = `${process.env.R2_PUBLIC_URL || process.env.NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_DOMAIN}/${fileName}`;
    console.log('R2 public URL:', r2Url);

    // 从数据库获取 prompts 记录
    console.log('Fetching prompt record from database...');
    const supabase = getSupabaseClient();
    const { data: promptRecord, error: fetchError } = await supabase
      .from('prompts')
      .select('*')
      .eq('prediction_id', predictionId)
      .single();

    if (fetchError || !promptRecord) {
      console.error('Error fetching prompt record:', fetchError);
      throw new Error('Prompt record not found for prediction: ' + predictionId);
    }

    console.log('Found prompt record:', promptRecord);

    try {
      // 更新 prompts 表状态
      console.log('Updating prompt with image URL...');
      const { error: promptUpdateError } = await supabase
        .from('prompts')
        .update({
          status: 'completed',
          image_url: r2Url,
          image_error: null
        })
        .eq('id', promptRecord.id);

      if (promptUpdateError) {
        console.error('Error updating prompt:', promptUpdateError);
        throw promptUpdateError;
      }
      console.log('Prompt updated successfully');

      // 保存到 images 表
      console.log('Saving image to database...');
      await createImage({
        category_id: promptRecord.category_id,
        prompt: input.prompt,
        image_url: r2Url,
        user_id: promptRecord.user_id,
        status: 'active',
        metadata: {
          predictionId,
          originalUrl: imageUrl,
          model: "minimax/image-01"
        }
      });
      console.log('Image saved to database successfully');

    } catch (error) {
      console.error('Database operation failed:', error);
      throw error;
    }

    console.log('Webhook processing completed successfully');
    return NextResponse.json({
      status: "success",
      imageUrl: r2Url,
      promptId: promptRecord.id
    });
  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Webhook processing failed" },
      { status: 500 }
    );
  }
}

// Exported POST method, wrapped with withApiLogger
export async function POST(req: NextRequest) {
  try {
    console.log('🔔 Webhook received:', new Date().toISOString())

    const body = await req.json()
    console.log('📦 Webhook body:', JSON.stringify(body, null, 2))

    const { id: predictionId, status, output, error } = body

    if (!predictionId) {
      console.error('❌ No prediction ID in webhook')
      return NextResponse.json({ error: 'No prediction ID' }, { status: 400 })
    }

    console.log(`📊 Processing webhook - ID: ${predictionId}, Status: ${status}`)

    // 1. 查找对应的 prompt
    console.log('🔍 Step 1: Finding prompt with prediction ID:', predictionId)
    const { data: prompt, error: findError } = await supabase
      .from('prompts')
      .select('*')
      .eq('prediction_id', predictionId)
      .single()

    if (findError) {
      console.error('❌ Error finding prompt:', findError)
      return NextResponse.json({ error: 'Prompt not found' }, { status: 404 })
    }

    if (!prompt) {
      console.error('❌ No prompt found for prediction ID:', predictionId)
      return NextResponse.json({ error: 'Prompt not found' }, { status: 404 })
    }

    console.log('✅ Step 1 Complete: Found prompt:', prompt.id)
    console.log('📋 Prompt details:', JSON.stringify(prompt, null, 2))

    // 2. 处理不同状态
    if (status === 'succeeded' && output && output.length > 0) {
      console.log('🎉 Step 2: Processing successful generation')
      console.log('📸 Output URLs:', output)

      try {
        // 2.1 获取图片数据
        console.log('📥 Step 2.1: Fetching image from:', output[0])
        const imageResponse = await fetch(output[0])
        if (!imageResponse.ok) {
          throw new Error(`Failed to fetch image: ${imageResponse.statusText}`)
        }
        const imageBuffer = await imageResponse.arrayBuffer()
        console.log(`✅ Step 2.1 Complete: Image fetched successfully, size: ${imageBuffer.byteLength} bytes`)

        // 2.2 上传到 R2
        const key = `generated/${prompt.id}/${Date.now()}.png`
        console.log('☁️ Step 2.2: Uploading to R2 with key:', key)
        console.log('🔧 R2 Config:', {
          bucket: process.env.R2_BUCKET_NAME || process.env.CLOUDFLARE_R2_BUCKET_NAME,
          endpoint: process.env.R2_ENDPOINT || `https://${process.env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com`
        })

        const command = new PutObjectCommand({
          Bucket: process.env.R2_BUCKET_NAME || process.env.CLOUDFLARE_R2_BUCKET_NAME || '',
          Key: key,
          Body: Buffer.from(imageBuffer),
          ContentType: 'image/png',
        })

        await s3Client.send(command)
        console.log('✅ Step 2.2 Complete: Image uploaded to R2 successfully')

        // 2.3 生成访问 URL
        const imageUrl = `${process.env.R2_PUBLIC_URL || process.env.NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_DOMAIN}/${key}`
        console.log('🔗 Step 2.3: Generated image URL:', imageUrl)

        // 2.4 更新数据库
        console.log('💾 Step 2.4: Updating database with image URL')
        const { error: updateError } = await supabase
          .from('prompts')
          .update({
            image_url: imageUrl,
            image_error: null,
            status: 'completed'
          })
          .eq('id', prompt.id)

        if (updateError) {
          console.error('❌ Error updating database:', updateError)
          throw updateError
        }
        console.log('✅ Step 2.4 Complete: Database updated successfully')

        // 2.5 验证更新
        console.log('🔍 Step 2.5: Verifying database update')
        const { data: updatedPrompt, error: verifyError } = await supabase
          .from('prompts')
          .select('*')
          .eq('id', prompt.id)
          .single()

        if (verifyError) {
          console.error('❌ Error verifying update:', verifyError)
        } else {
          console.log('✅ Step 2.5 Complete: Verified prompt data:', JSON.stringify(updatedPrompt, null, 2))
        }

      } catch (error) {
        console.error('❌ Error processing successful generation:', error)
        // 记录错误到数据库
        console.log('💾 Recording error to database')
        await supabase
          .from('prompts')
          .update({
            image_error: error instanceof Error ? error.message : 'Failed to process image',
            status: 'failed'
          })
          .eq('id', prompt.id)
        throw error
      }

    } else if (status === 'failed') {
      console.log('❌ Step 2: Processing failed generation')
      console.log('💾 Recording failure to database')
      // 记录错误到数据库
      const { error: updateError } = await supabase
        .from('prompts')
        .update({
          image_error: error || 'Generation failed',
          status: 'failed'
        })
        .eq('id', prompt.id)

      if (updateError) {
        console.error('❌ Error updating database with failure:', updateError)
        throw updateError
      }
      console.log('✅ Failure recorded in database')
    } else {
      console.log(`⏳ Step 2: Ignoring status '${status}' - not processing`)
    }

    console.log('🎉 Webhook processing completed successfully')
    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Webhook processing error:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
} 