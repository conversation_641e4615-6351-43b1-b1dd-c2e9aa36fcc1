import { NextRequest, NextResponse } from "next/server";

export const runtime = "edge";

export async function POST(req: NextRequest) {
  try {
    const { agentName } = await req.json();
    
    if (!agentName) {
      return NextResponse.json(
        { error: "未提供智能体名称" },
        { status: 400 }
      );
    }

    // 获取智能体描述
    const descriptionResponse = await fetch("https://api.together.xyz/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${process.env.TOGETHER_API_KEY}`,
      },
      body: JSON.stringify({
        messages: [
          {
            role: "system",
            content: "你是一个AI助手配置专家。请为用户提供的智能体名称生成一段简短的描述（50-100字），描述该智能体的功能和特点。直接输出描述文本，不要添加任何格式标记或额外内容。",
          },
          {
            role: "user",
            content: `请为名为"${agentName}"的智能体生成描述`,
          },
        ],
        model: "meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8",
        max_tokens: 1024,
      }),
    });

    if (!descriptionResponse.ok) {
      throw new Error("生成智能体描述失败");
    }

    const descriptionData = await descriptionResponse.json();
    const description = descriptionData.choices[0].message.content.trim();

    // 获取适用人群
    const targetAudienceResponse = await fetch("https://api.together.xyz/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${process.env.TOGETHER_API_KEY}`,
      },
      body: JSON.stringify({
        messages: [
          {
            role: "system",
            content: "你是一个AI助手配置专家。请为用户提供的智能体名称生成3-5类适用人群，每类人群配备简短的说明，解释该智能体对这类用户的好处。必须以JSON格式返回，格式为对象，其中键为人群类别（如\"学生\"、\"研究人员\"、\"教师\"等），值为对应的好处描述。例如：{\"学生\": \"提高学习效率，快速获取知识\", \"专业人士\": \"节省时间，提升工作质量\"}。只返回JSON对象，不要添加任何其他文字或标记。",
          },
          {
            role: "user",
            content: `请为名为"${agentName}"的智能体生成适用人群描述`,
          },
        ],
        model: "meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8",
        max_tokens: 1024,
      }),
    });

    if (!targetAudienceResponse.ok) {
      throw new Error("生成适用人群失败");
    }

    const targetAudienceData = await targetAudienceResponse.json();
    const targetAudience = targetAudienceData.choices[0].message.content.trim();

    // 获取特点
    const featuresResponse = await fetch("https://api.together.xyz/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${process.env.TOGETHER_API_KEY}`,
      },
      body: JSON.stringify({
        messages: [
          {
            role: "system",
            content: "你是一个AI助手配置专家。请为用户提供的智能体名称生成3-5条主要特点，每条特点包含标题和描述。必须以JSON格式返回，格式为对象，其中键为特点标题（如\"速度快\"、\"准确性高\"等简短标签），值为对应的详细描述。例如：{\"速度快\": \"AI自动化处理，节省大量时间\", \"多语言支持\": \"可处理多种语言的内容翻译\"}。只返回JSON对象，不要添加任何其他文字或标记。",
          },
          {
            role: "user",
            content: `请为名为"${agentName}"的智能体生成主要特点`,
          },
        ],
        model: "meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8",
        max_tokens: 1024,
      }),
    });

    if (!featuresResponse.ok) {
      throw new Error("生成特点失败");
    }

    const featuresData = await featuresResponse.json();
    const features = featuresData.choices[0].message.content.trim();

    // 获取FAQ
    const faqResponse = await fetch("https://api.together.xyz/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${process.env.TOGETHER_API_KEY}`,
      },
      body: JSON.stringify({
        messages: [
          {
            role: "system",
            content: "你是一个AI助手配置专家。请为用户提供的智能体名称生成3-5个常见问题及答案(FAQ)，每个问题和答案应该简明扼要。必须以JSON格式返回，格式为: {\"faqs\": [{\"id\": \"1\", \"question\": \"问题1\", \"answer\": \"答案1\"}, {\"id\": \"2\", \"question\": \"问题2\", \"answer\": \"答案2\"}, ...]}。只返回JSON，不要添加任何其他文字或标记。",
          },
          {
            role: "user",
            content: `请为名为"${agentName}"的智能体生成FAQ`,
          },
        ],
        model: "meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8",
        max_tokens: 2048,
      }),
    });

    if (!faqResponse.ok) {
      throw new Error("生成FAQ失败");
    }

    const faqData = await faqResponse.json();
    const faq = faqData.choices[0].message.content.trim();

    // 获取系统提示词
    const promptResponse = await fetch("https://api.together.xyz/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${process.env.TOGETHER_API_KEY}`,
      },
      body: JSON.stringify({
        messages: [
          {
            role: "system",
            content: "你是一个AI助手配置专家。请为用户提供的智能体名称生成一段详细的系统提示词，用于指导AI如何扮演该角色并回应用户。直接输出提示词文本，不要添加任何格式标记或额外内容。",
          },
          {
            role: "user",
            content: `请为名为"${agentName}"的智能体生成系统提示词`,
          },
        ],
        model: "meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8",
        max_tokens: 2048,
      }),
    });

    if (!promptResponse.ok) {
      throw new Error("生成系统提示词失败");
    }

    const promptData = await promptResponse.json();
    const systemPrompt = promptData.choices[0].message.content.trim();

    // 预设几种图像提示词
    const imagePrompts = [
      "professional assistant, digital avatar, minimalist design, blue and white color scheme, friendly expression",
      "AI assistant character, 3D render, professional appearance, tech-inspired, digital art style",
      "friendly robot assistant, cartoon style, cute design, helpful expression, soft colors",
      "professional AI agent portrait, digital art, modern tech aesthetic, glowing blue accents",
      "stylized character portrait, AI assistant, digital art style, professional appearance"
    ];
    
    // 随机选择一个图像提示词
    const imagePrompt = imagePrompts[Math.floor(Math.random() * imagePrompts.length)];

    return NextResponse.json({
      description,
      targetAudience,
      features,
      faq,
      systemPrompt,
      imagePrompt
    });
  } catch (error) {
    console.error("Error processing agent name:", error);
    return NextResponse.json(
      { error: "处理智能体名称失败" },
      { status: 500 }
    );
  }
} 