import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import { ImageHistory } from '@/lib/imageHistory'
export const runtime = "edge";

// 获取公共（热门）图片
export async function GET(request: NextRequest) {
  try {
    // 从URL获取查询参数
    const searchParams = request.nextUrl.searchParams
    // 获取limit参数，默认为20
    const limitParam = searchParams.get('limit')
    const limit = limitParam ? parseInt(limitParam, 10) : 20
    
    // 从数据库获取公共图片
    // 这里假设你有一个images表，我们获取最新的几张图片作为公共展示
    const { data, error } = await supabase
      .from('images')
      .select('id, prompt, image_url, created_at')
      .order('created_at', { ascending: false })
      .limit(limit);
    
    if (error) {
      console.error('获取公共图片失败:', error)
      return NextResponse.json(
        { error: "获取公共图片失败" },
        { status: 500 }
      )
    }
    
    // 添加缓存头，缓存10分钟
    return NextResponse.json(data || [], {
      headers: {
        'Cache-Control': 'public, max-age=600, s-maxage=600, stale-while-revalidate=600'
      }
    })
  } catch (error) {
    console.error('获取公共图片失败:', error)
    return NextResponse.json(
      { error: "获取公共图片失败" },
      { status: 500 }
    )
  }
}

// 处理OPTIONS请求（CORS预检请求）
export async function OPTIONS() {
  return new NextResponse(null, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
} 