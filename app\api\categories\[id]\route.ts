import { NextRequest, NextResponse } from "next/server";
import { getSupabaseClient } from "@/models/db";
import type { Category } from "@/models/category";
export const runtime = "edge";

interface CategoryDB {
  id: string;
  name: string;
  slug: string;
  description: string;
  icon?: string;
  seo_title?: string;
  seo_description?: string;
  generator_config?: any;
  shares_config?: any;
  faq_config?: any;
  created_at: string;
  updated_at: string;
}

function formatCategory(category: CategoryDB): Category {
  return {
    id: category.id,
    name: category.name,
    slug: category.slug,
    description: category.description,
    icon: category.icon,
    seoTitle: category.seo_title,
    seoDescription: category.seo_description,
    generatorConfig: category.generator_config,
    sharesConfig: category.shares_config,
    faqConfig: category.faq_config,
    createdAt: new Date(category.created_at),
    updatedAt: new Date(category.updated_at)
  };
}

// GET a single category by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const supabase = getSupabaseClient();
    
    const { data, error } = await supabase
      .from("categories")
      .select("*")
      .eq("id", id)
      .single();

    if (error) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(formatCategory(data as CategoryDB));
  } catch (error) {
    console.error("Error fetching category:", error);
    return NextResponse.json(
      { error: "Failed to fetch category" },
      { status: 500 }
    );
  }
}

// DELETE a category
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const supabase = getSupabaseClient();
    
    const { error } = await supabase
      .from("categories")
      .delete()
      .eq("id", id);

    if (error) {
      console.error("Error deleting category:", error);
      return NextResponse.json(
        { error: "Failed to delete category" },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting category:", error);
    return NextResponse.json(
      { error: "Failed to delete category" },
      { status: 500 }
    );
  }
}

// UPDATE a category
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    const supabase = getSupabaseClient();
    
    // Remove read-only fields
    const { id: _, createdAt, updatedAt, ...updateData } = body;
    
    // Convert camelCase to snake_case for the database
    const dbData = {
      name: updateData.name,
      slug: updateData.slug,
      description: updateData.description,
      icon: updateData.icon,
      seo_title: updateData.seoTitle,
      seo_description: updateData.seoDescription,
      generator_config: updateData.generatorConfig,
      shares_config: updateData.sharesConfig,
      faq_config: updateData.faqConfig
    };
    
    const { data, error } = await supabase
      .from("categories")
      .update(dbData)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      console.error("Error updating category:", error);
      return NextResponse.json(
        { error: "Failed to update category" },
        { status: 500 }
      );
    }

    return NextResponse.json(formatCategory(data as CategoryDB));
  } catch (error) {
    console.error("Error updating category:", error);
    return NextResponse.json(
      { error: "Failed to update category" },
      { status: 500 }
    );
  }
} 