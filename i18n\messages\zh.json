{"metadata": {"title": "几小时内构建任何 AI SaaS 创业项目 | ShipAny", "description": "ShipAny 是一个用于构建 AI SaaS 创业项目的 NextJS 模板，提供各种模板和组件，帮助您快速启动。", "keywords": "Ship<PERSON>ny, AI SaaS 模板, NextJS 模板"}, "user": {"sign_in": "登录", "sign_out": "退出登录", "credits": "额度", "api_keys": "API 密钥", "my_orders": "我的订单", "user_center": "用户中心", "admin_system": "管理后台"}, "sign_modal": {"sign_in_title": "登录", "sign_in_description": "登录您的账户", "sign_up_title": "注册", "sign_up_description": "创建新账户", "email_title": "邮箱", "email_placeholder": "请输入您的邮箱", "password_title": "密码", "password_placeholder": "请输入您的密码", "email": "邮箱", "password": "密码", "confirm_password": "确认密码", "forgot_password": "忘记密码？", "login": "登录", "logging_in": "登录中...", "sign_up": "注册", "signing_up": "注册中...", "or": "或", "or_continue_with": "或继续使用", "continue": "继续", "no_account": "还没有账户？", "already_have_account": "已有账户？", "email_sign_in": "使用邮箱登录", "google_sign_in": "使用 Google 登录", "github_sign_in": "使用 GitHub 登录", "close_title": "关闭", "cancel_title": "取消", "login_failed": "登录失败，请检查您的邮箱和密码", "signup_failed": "注册失败，请稍后再试", "signup_success": "注册成功！", "check_email": "请检查您的邮箱以完成验证", "passwords_not_match": "两次输入的密码不匹配", "terms_agreement": "注册即表示您同意我们的", "terms_of_service": "服务条款", "and": "和", "privacy_policy": "隐私政策", "reset_password_title": "重置密码", "reset_password_description": "输入您的邮箱地址，我们将发送密码重置链接", "email_required": "请输入邮箱地址", "send_reset_link": "发送重置链接", "sending": "发送中...", "reset_password_sent": "重置链接已发送", "reset_password_failed": "发送重置链接失败", "reset_password_check_email": "请检查您的邮箱，点击重置链接完成密码重置", "back_to_login": "返回登录", "set_new_password": "设置新密码", "set_new_password_description": "请输入您的新密码", "new_password": "新密码", "reset_password": "重置密码", "resetting": "重置中...", "password_reset_success": "密码重置成功", "password_reset_failed": "密码重置失败", "password_reset_success_message": "您的密码已成功重置，现在可以使用新密码登录"}, "my_orders": {"title": "我的订单", "description": "在 ShipAny 上购买的订单。", "no_orders": "未找到订单", "tip": "", "activate_order": "激活订单", "actived": "已激活", "join_discord": "加入 Discord", "read_docs": "阅读文档", "table": {"order_no": "订单号", "email": "邮箱", "product_name": "产品名称", "amount": "金额", "paid_at": "支付时间", "github_username": "GitHub 用户名", "status": "状态"}}, "my_credits": {"title": "我的积分", "left_tip": "剩余积分: {left_credits}", "no_credits": "没有积分记录", "recharge": "充值", "table": {"trans_no": "交易号", "trans_type": "交易类型", "credits": "积分", "updated_at": "更新时间", "status": "状态"}}, "api_keys": {"title": "API 密钥", "tip": "请妥善保管您的 API 密钥，避免泄露", "no_api_keys": "没有 API 密钥", "create_api_key": "创建 API 密钥", "table": {"name": "名称", "key": "密钥", "created_at": "创建时间"}, "form": {"name": "名称", "name_placeholder": "API 密钥名称", "submit": "提交"}}, "blog": {"title": "博客", "description": "关于 ShipAny 的新闻、资源和更新", "read_more_text": "阅读更多"}, "my_invites": {"title": "我的邀请", "description": "查看您的邀请记录", "no_invites": "未找到邀请记录", "my_invite_link": "我的邀请链接", "edit_invite_link": "编辑邀请链接", "copy_invite_link": "复制邀请链接", "invite_code": "邀请码", "invite_tip": "每邀请 1 位朋友购买 ShipAny，奖励 $50。", "invite_balance": "邀请奖励余额", "total_invite_count": "总邀请人数", "total_paid_count": "已充值人数", "total_award_amount": "总奖励金额", "update_invite_code": "设置邀请码", "update_invite_code_tip": "输入你的自定义邀请码", "update_invite_button": "保存", "no_orders": "你需要先购买过 ShipAny 才能邀请朋友", "no_affiliates": "你暂无邀请朋友的权限，请联系我们申请开通。", "table": {"invite_time": "邀请时间", "invite_user": "邀请用户", "status": "状态", "reward_percent": "奖励比例", "reward_amount": "奖励金额", "pending": "已注册，未支付", "completed": "已支付"}}}