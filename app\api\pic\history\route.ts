import { NextRequest, NextResponse } from 'next/server'
import { getImagesByUser, getUserUuidFromRequest } from "@/lib/imageHistory"
export const runtime = "edge";

export async function GET(request: NextRequest) {
  try {
    // 从URL获取查询参数
    const searchParams = request.nextUrl.searchParams
    const userId = searchParams.get('userId')
    // 获取limit参数，默认为20
    const limitParam = searchParams.get('limit')
    const limit = limitParam ? parseInt(limitParam, 10) : 20
    
    // 如果提供了userId参数，则直接获取该用户的图片
    if (userId) {
      // 获取指定用户的图片历史
      const images = await getImagesByUser(userId, limit)
      
      // 添加缓存头，缓存5分钟
      return NextResponse.json(images, {
        headers: {
          'Cache-Control': 'public, max-age=300, s-maxage=300, stale-while-revalidate=300'
        }
      })
    }
    
    // 从请求中获取用户ID
    const userUuid = await getUserUuidFromRequest(request)
    
    if (!userUuid) {
      return NextResponse.json(
        { error: "您需要登录才能查看历史记录" },
        { status: 401 }
      )
    }
    
    // 获取当前登录用户的图片历史
    const images = await getImagesByUser(userUuid, limit)
    
    // 添加缓存头，缓存2分钟
    return NextResponse.json(images, {
      headers: {
        'Cache-Control': 'private, max-age=120, s-maxage=120, stale-while-revalidate=120'
      }
    })
  } catch (error) {
    console.error('获取历史记录失败:', error)
    return NextResponse.json(
      { error: "获取历史记录失败" },
      { status: 500 }
    )
  }
}

// 处理OPTIONS请求（CORS预检请求）
export async function OPTIONS() {
  return new NextResponse(null, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
} 