"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Copy, Check, Search, Trash2 } from "lucide-react"
import { toast } from "sonner"
import { useSession } from "next-auth/react"
import { Prompt, getPromptsByUser } from "@/lib/promptHistory"
import DashboardClient from "@/components/dash/DashboardClient"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { formatDate } from "@/lib/utils"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

interface Category {
  id: string
  name: string
}

const PromptHistory = () => {
  const [prompts, setPrompts] = useState<Prompt[]>([])
  const [filteredPrompts, setFilteredPrompts] = useState<Prompt[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>("all")
  const [categories, setCategories] = useState<Category[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [copiedId, setCopiedId] = useState<string | null>(null)
  const { data: session, status } = useSession()
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [selectedPrompt, setSelectedPrompt] = useState<Prompt | null>(null)
  const [editedKeyword, setEditedKeyword] = useState("")
  const [editedOptimizedPrompt, setEditedOptimizedPrompt] = useState("")
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [promptToDelete, setPromptToDelete] = useState<string | null>(null)

  const fetchPrompts = async () => {
    setIsLoading(true)
    try {
      console.log('Fetching all prompts')
      const userPrompts = await getPromptsByUser('')
      console.log('Fetched prompts:', userPrompts)
      setPrompts(userPrompts)
      setFilteredPrompts(userPrompts)
    } catch (error) {
      console.error("获取提示词历史失败:", error)
      toast.error("获取提示词历史失败")
      setPrompts([])
      setFilteredPrompts([])
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch("/api/categories")
        if (!response.ok) throw new Error("获取类别失败")
        const data = await response.json()
        setCategories(data)
      } catch (error) {
        console.error("获取类别失败:", error)
        toast.error("获取类别失败")
      }
    }
    fetchCategories()
  }, [])

  // Fetch prompts
  useEffect(() => {
    fetchPrompts()
  }, [])

  // Filter prompts
  useEffect(() => {
    console.log('Filtering prompts:', { 
      totalPrompts: prompts.length, 
      searchTerm, 
      selectedCategory 
    })
    
    let filtered = [...prompts]
    
    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(prompt => 
        prompt.keyword.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (prompt.optimized_prompt?.toLowerCase() || '').includes(searchTerm.toLowerCase())
      )
    }
    
    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(prompt => prompt.category_id === selectedCategory)
    }
    
    console.log('Filtered results:', filtered)
    setFilteredPrompts(filtered)
  }, [searchTerm, selectedCategory, prompts])

  const handleCopy = async (text: string, id: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedId(id)
      toast.success("已复制到剪贴板")
      setTimeout(() => setCopiedId(null), 2000)
    } catch (err) {
      toast.error("复制失败")
    }
  }

  const handleDelete = async (id: string) => {
    setPromptToDelete(id)
    setIsDeleteDialogOpen(true)
  }

  const confirmDelete = async () => {
    if (!promptToDelete) return

    try {
      const response = await fetch(`/api/prompts/${promptToDelete}`, {
        method: 'DELETE',
      })
      
      if (!response.ok) {
        throw new Error('删除失败')
      }
      
      toast.success("删除成功")
      fetchPrompts()
    } catch (err) {
      console.error("删除失败:", err)
      toast.error("删除失败")
    } finally {
      setIsDeleteDialogOpen(false)
      setPromptToDelete(null)
    }
  }

  const handleEdit = async () => {
    if (!selectedPrompt) return

    try {
      const response = await fetch(`/api/prompts/${selectedPrompt.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          keyword: editedKeyword,
          optimized_prompt: editedOptimizedPrompt,
        }),
      })
      
      if (!response.ok) {
        throw new Error('保存失败')
      }
      
      toast.success("保存成功")
      setIsDialogOpen(false)
      fetchPrompts()
    } catch (err) {
      console.error("保存失败:", err)
      toast.error("保存失败")
    }
  }

  const openEditDialog = (prompt: Prompt) => {
    setSelectedPrompt(prompt)
    setEditedKeyword(prompt.keyword)
    setEditedOptimizedPrompt(prompt.optimized_prompt || "")
    setIsDialogOpen(true)
  }

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
      </div>
    )
  }

  if (status !== "authenticated") {
    return (
      <Card className="bg-gray-900/50 border-gray-800 backdrop-blur-sm">
        <CardContent className="flex flex-col items-center justify-center p-6 space-y-4">
          <p className="text-gray-400">请先登录以查看历史记录</p>
          <Button 
            onClick={() => window.location.href = "/login"}
            className="bg-emerald-600 hover:bg-emerald-700"
          >
            登录
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="bg-gray-900/50 border-gray-800 backdrop-blur-sm h-full">
      <CardContent className="p-6 h-full flex flex-col">
        <div className="space-y-6 flex-1 overflow-hidden flex flex-col">
          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Label className="text-gray-300 mb-2 block">搜索</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="搜索关键词或提示词..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8 bg-gray-800/50 border-gray-700 text-gray-300"
                />
              </div>
            </div>
            <div className="w-full sm:w-48">
              <Label className="text-gray-300 mb-2 block">类别</Label>
              <Select 
                value={selectedCategory} 
                onValueChange={setSelectedCategory}
              >
                <SelectTrigger className="bg-gray-800/50 border-gray-700 text-gray-300">
                  <SelectValue placeholder="选择类别" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-700">
                  <SelectItem value="all" className="text-gray-300 hover:bg-gray-700">
                    全部类别
                  </SelectItem>
                  {categories.map((category) => (
                    <SelectItem 
                      key={category.id} 
                      value={category.id}
                      className="text-gray-300 hover:bg-gray-700"
                    >
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Results */}
          {isLoading ? (
            <div className="flex items-center justify-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
            </div>
          ) : filteredPrompts.length > 0 ? (
            <div className="border border-gray-800 rounded-lg overflow-hidden flex-1 flex flex-col">
              <div className="overflow-auto flex-1">
                <Table>
                  <TableHeader className="sticky top-0 bg-gray-800/50 z-10">
                    <TableRow className="bg-gray-800/50">
                      <TableHead className="text-gray-300 w-[15%] py-3">关键词</TableHead>
                      <TableHead className="text-gray-300 w-[45%] py-3">优化提示词</TableHead>
                      <TableHead className="text-gray-300 w-[10%] py-3">状态</TableHead>
                      <TableHead className="text-gray-300 w-[20%] py-3">创建时间</TableHead>
                      <TableHead className="text-gray-300 w-[10%] py-3">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredPrompts.map((prompt) => (
                      <TableRow key={prompt.id} className="border-gray-800 hover:bg-gray-800/30">
                        <TableCell className="text-gray-300 font-medium py-3">
                          <div 
                            className="truncate max-w-[200px] cursor-pointer hover:text-white" 
                            title={prompt.keyword}
                            onClick={() => openEditDialog(prompt)}
                          >
                            {prompt.keyword}
                          </div>
                        </TableCell>
                        <TableCell className="text-gray-300 py-3">
                          <div className="truncate max-w-[600px]" title={prompt.optimized_prompt}>
                            {prompt.optimized_prompt}
                          </div>
                        </TableCell>
                        <TableCell className="py-3">
                          <span className={`px-2 py-1 rounded-full text-sm whitespace-nowrap ${
                            prompt.status === 'completed' 
                              ? 'bg-green-700 text-green-200'
                              : prompt.status === 'failed'
                              ? 'bg-red-700 text-red-200'
                              : 'bg-yellow-700 text-yellow-200'
                          }`}>
                            {prompt.status === 'completed' ? '完成' : 
                             prompt.status === 'failed' ? '失败' : 
                             prompt.status === 'processing' ? '处理中' : '等待中'}
                          </span>
                        </TableCell>
                        <TableCell className="text-gray-300 py-3 whitespace-nowrap">
                          {prompt.created_at ? formatDate(prompt.created_at) : '-'}
                        </TableCell>
                        <TableCell className="py-3">
                          <div className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleCopy(prompt.optimized_prompt || prompt.keyword, prompt.id)}
                              className="text-gray-400 hover:text-white"
                            >
                              {copiedId === prompt.id ? (
                                <Check className="h-4 w-4" />
                              ) : (
                                <Copy className="h-4 w-4" />
                              )}
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDelete(prompt.id)}
                              className="text-gray-400 hover:text-red-400"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center p-8 bg-gray-800/50 rounded-lg flex-1">
              <p className="text-gray-400">暂无历史记录</p>
            </div>
          )}
        </div>
      </CardContent>

      {/* Edit Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="bg-gray-900 border-gray-800 text-white">
          <DialogHeader>
            <DialogTitle>编辑提示词</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label className="text-gray-300">关键词</Label>
              <Input
                value={editedKeyword}
                onChange={(e) => setEditedKeyword(e.target.value)}
                className="bg-gray-800 border-gray-700 text-gray-300"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-gray-300">优化提示词</Label>
              <Textarea
                value={editedOptimizedPrompt}
                onChange={(e) => setEditedOptimizedPrompt(e.target.value)}
                className="bg-gray-800 border-gray-700 text-gray-300 min-h-[100px]"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDialogOpen(false)}
              className="border-gray-700 text-gray-300 hover:bg-gray-800"
            >
              取消
            </Button>
            <Button
              onClick={handleEdit}
              className="bg-blue-600 hover:bg-blue-700"
            >
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent className="bg-gray-900 border-gray-800 text-white">
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription className="text-gray-400">
              确定要删除这条提示词记录吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="border-gray-700 text-gray-300 hover:bg-gray-800">
              取消
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  )
}

export default PromptHistory 