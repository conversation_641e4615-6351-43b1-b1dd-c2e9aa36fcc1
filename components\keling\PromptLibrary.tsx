"use client"

import { useState, useEffect, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Loader2, Plus, Trash2, Co<PERSON>, Check } from "lucide-react"
import { toast } from "sonner"
import { useSession } from "next-auth/react"
import Image from "next/image"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Prompt, getPromptsByUser, createPrompt, updatePrompt } from "@/lib/promptHistory"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

interface Category {
  id: string
  name: string
}

interface GeneratedPrompt {
  id: string
  keyword: string
  optimized_prompt: string
  category_id: string
  created_at?: string
  status?: string
  user_id?: string
}

const MAX_CONCURRENT_TASKS = 3
const MAX_RETRIES = 3

// Use the provided default UUID for processing if session.user.id is not available
const FALLBACK_USER_ID = "47618b3f-6be6-473f-93e3-8d624efdc88c"

const PromptLibrary = () => {
  const [keyword, setKeyword] = useState("")
  const [prompts, setPrompts] = useState<GeneratedPrompt[]>([])
  const [isGenerating, setIsGenerating] = useState(false)
  const [categories, setCategories] = useState<Category[]>([])
  const [selectedCategory, setSelectedCategory] = useState("")
  const [isLoadingCategories, setIsLoadingCategories] = useState(true)
  const [showLoginDialog, setShowLoginDialog] = useState(false)
  const { data: session, status } = useSession()
  const [copiedId, setCopiedId] = useState<string | null>(null)

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      setIsLoadingCategories(true)
      try {
        const response = await fetch("/api/categories")
        if (!response.ok) throw new Error("获取类别失败")
        const data = await response.json()
        setCategories(data)
        if (data.length > 0) setSelectedCategory(data[0].id)
      } catch (error) {
        console.error("获取类别失败:", error)
        toast.error(error instanceof Error ? error.message : "获取类别失败")
      } finally {
        setIsLoadingCategories(false)
      }
    }
    fetchCategories()
  }, [])

  // Fetch user's prompt history
  const fetchPromptHistory = useCallback(async () => {
    if (status === "authenticated" && session?.user?.id) {
      try {
        const userPrompts = await getPromptsByUser(session.user.id)
        setPrompts(userPrompts)
      } catch (error) {
        console.error("获取提示词历史失败:", error)
        toast.error("获取提示词历史失败")
      }
    } else if (status !== "authenticated") {
      setPrompts([]);
    }
  }, [status, session?.user?.id])

  useEffect(() => {
    if (status === "authenticated" && session?.user) {
       fetchPromptHistory();
    } else if (status !== "loading") {
       setPrompts([]);
    }
  }, [status, session?.user, fetchPromptHistory])

  const generatePrompt = async () => {
    if (!keyword.trim()) {
      toast.info("请输入关键词")
      return
    }
    if (!selectedCategory) {
      toast.info("请选择类别")
      return
    }

    setIsGenerating(true)
    const userId = session?.user?.id || FALLBACK_USER_ID

    try {
      // 调用优化API
      const response = await fetch("/api/zhiti", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ 
          instruction: keyword,
          userId,
          categoryId: selectedCategory
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || "生成提示词失败")
      }

      const data = await response.json()
      const optimizedPrompts = data.prompts as GeneratedPrompt[]

      // 立即显示生成的提示词
      setPrompts(prev => [...optimizedPrompts, ...prev])
      toast.success(`成功生成 ${optimizedPrompts.length} 个提示词`)
      setKeyword("")
    } catch (error) {
      console.error('生成提示词失败:', error)
      toast.error(`生成提示词失败: ${error instanceof Error ? error.message : '未知错误'}`)
    } finally {
      setIsGenerating(false)
    }
  }

  const handleCopy = async (text: string, id: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedId(id)
      toast.success("已复制到剪贴板")
      setTimeout(() => setCopiedId(null), 2000)
    } catch (err) {
      toast.error("复制失败")
    }
  }

  // UI Rendering (Login Dialog, Main Card, etc.) - largely unchanged but ensure disabled states are correct.
  const LoginDialog = () => (
    <Dialog open={showLoginDialog} onOpenChange={setShowLoginDialog}>
      <DialogContent className="bg-gray-900 border-gray-700 text-white">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-amber-400">请先登录</DialogTitle>
          <DialogDescription className="text-gray-300">
            登录后即可使用提示词生成功能。
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <Card className="bg-gradient-to-r from-gray-800 to-gray-900 border-gray-700 p-6 flex flex-col items-center justify-center shadow-md">
            <div className="text-lg text-white font-medium flex items-center mb-4">加入我们的创意社区</div>
            <div className="text-sm text-gray-300 mb-6 text-center">
              创建账号以访问AI图片生成、保存您的创作并解锁高级功能。
            </div>
            <Button 
              className="w-full bg-emerald-600 hover:bg-emerald-700 text-white mb-2"
              onClick={() => {
                setShowLoginDialog(false)
                window.location.href = "/login"
              }}
            >
              立即登录
            </Button>
          </Card>
        </div>
        <DialogFooter className="flex flex-col sm:flex-row gap-2">
          <Button variant="ghost" className="text-gray-400 hover:text-gray-200" onClick={() => setShowLoginDialog(false)}>
            继续浏览
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )

  if (status === "loading") {
    return (
      <Card className="w-full bg-gray-900 border-gray-800">
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="h-6 w-6 animate-spin text-white" />
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      {/* Left Column - Input Section */}
      <Card className="bg-gray-900/50 border-gray-800 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-white">生成提示词</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Keyword Input */}
            <div className="space-y-2">
              <Label className="text-gray-300">输入关键词</Label>
              <Input
                value={keyword}
                onChange={(e) => setKeyword(e.target.value)}
                placeholder="输入关键词..."
                className="bg-gray-800/50 border-gray-700 text-gray-300"
                onKeyPress={(e) => e.key === 'Enter' && generatePrompt()}
                disabled={isGenerating}
              />
            </div>

            {/* Category Selection */}
            <div className="space-y-2">
              <Label className="text-gray-300">选择类别</Label>
              <Select 
                value={selectedCategory} 
                onValueChange={setSelectedCategory}
                disabled={isLoadingCategories || isGenerating}
              >
                <SelectTrigger className="bg-gray-800/50 border-gray-700 text-gray-300">
                  <SelectValue placeholder={isLoadingCategories ? "加载中..." : "选择图片类别"} />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-700">
                  {categories.map((category) => (
                    <SelectItem 
                      key={category.id} 
                      value={category.id}
                      className="text-gray-300 hover:bg-gray-700"
                    >
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Generate Button */}
            <Button
              onClick={generatePrompt}
              disabled={
                status !== 'authenticated' || 
                isGenerating || 
                !keyword.trim() || 
                isLoadingCategories
              }
              className="w-full bg-emerald-600 hover:bg-emerald-700"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  生成中...
                </>
              ) : (
                "生成提示词"
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Right Column - Results Section */}
      <Card className="bg-gray-900/50 border-gray-800 backdrop-blur-sm h-full">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-white">生成结果</CardTitle>
        </CardHeader>
        <CardContent>
          {prompts.length > 0 ? (
            <div className="space-y-4 max-h-[800px] overflow-y-auto pr-2">
              {prompts.map((prompt) => (
                <Card key={prompt.id} className="bg-gray-800/50 border-gray-700">
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-300 font-medium">关键词: {prompt.keyword}</span>
                        <span className="text-sm px-2 py-1 rounded-full bg-green-700 text-green-200">
                          完成
                        </span>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-semibold text-gray-300">优化提示词:</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleCopy(prompt.optimized_prompt, prompt.id)}
                            className="text-gray-400 hover:text-white"
                          >
                            {copiedId === prompt.id ? (
                              <Check className="h-4 w-4" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                        <div className="bg-gray-900/50 p-3 rounded-md">
                          <p className="text-gray-300 text-sm whitespace-pre-wrap break-words">
                            {prompt.optimized_prompt}
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="flex items-center justify-center h-48 bg-gray-800/50 rounded-lg">
              <p className="text-gray-400">暂无生成结果</p>
            </div>
          )}
        </CardContent>
      </Card>

      <LoginDialog />
    </div>
  )
}

export default PromptLibrary 