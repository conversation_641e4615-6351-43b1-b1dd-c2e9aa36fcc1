import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
export const runtime = "edge";

// 创建Supabase客户端
// 使用环境变量或提供默认值防止构建失败
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL || '';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || process.env.SUPABASE_ANON_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY || '';

// 创建客户端，即使URL为空也不会导致构建失败
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: false
  }
});

export async function GET(request: NextRequest) {
  try {
    // 从URL获取查询参数
    const searchParams = request.nextUrl.searchParams
    const userId = searchParams.get('userId') || '47618b3f-6be6-473f-93e3-8d624efdc88c'
    const limit = parseInt(searchParams.get('limit') || '10')
    
    console.log(`查询imagesing表, 用户ID: ${userId}, 限制: ${limit}`)
    
    // 查询imagesing表
    const { data, error } = await supabase
      .from('imagesing')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit)
    
    if (error) {
      console.error('查询imagesing表失败:', error)
      return NextResponse.json(
        { error: "获取数据失败", details: error.message },
        { status: 500 }
      )
    }
    
    if (!data || data.length === 0) {
      console.log(`没有找到用户 ${userId} 的数据`)
      // 返回空数组而不是错误，让前端可以正确处理
      return NextResponse.json([])
    }
    
    console.log(`找到 ${data.length} 条记录`)
    
    // 确保返回的数据格式正确
    return NextResponse.json(data, {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      }
    })
  } catch (error: any) {
    console.error('获取imagesing数据失败:', error)
    return NextResponse.json(
      { error: "获取数据失败", details: error?.message || String(error) },
      { status: 500 }
    )
  }
}

// 处理OPTIONS请求（CORS预检请求）
export async function OPTIONS() {
  return new NextResponse(null, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
} 