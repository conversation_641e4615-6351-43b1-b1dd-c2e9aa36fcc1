import { createClient } from '@supabase/supabase-js'

// 检查环境变量
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables')
}

const supabase = createClient(
  supabaseUrl || '',
  supabaseAnonKey || ''
)

export interface Prompt {
  id: string
  user_id: string
  keyword: string
  optimized_prompt: string
  category_id: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  error?: string | null
  created_at?: string
  updated_at?: string
}

// 获取所有提示词历史
export async function getPromptsByUser(userId: string): Promise<Prompt[]> {
  try {
    console.log('Fetching all prompts')
    
    const { data, error } = await supabase
      .from('prompts')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching prompts:', error)
      throw error
    }

    console.log('Fetched prompts:', data?.length || 0)
    return data || []
  } catch (error) {
    console.error('Error in getPromptsByUser:', error)
    return []
  }
}

// 创建新的提示词记录
export async function createPrompt(prompt: Omit<Prompt, 'id' | 'created_at' | 'updated_at'>): Promise<Prompt | null> {
  try {
    // 打印完整的请求数据
    console.log('准备创建提示词，完整数据:', JSON.stringify(prompt, null, 2))
    
    // 确保所有必需字段都存在
    if (!prompt.user_id) throw new Error('user_id 是必需的')
    if (!prompt.keyword) throw new Error('keyword 是必需的')
    if (!prompt.optimized_prompt) throw new Error('optimized_prompt 是必需的')
    if (!prompt.category_id) throw new Error('category_id 是必需的')
    if (!prompt.status) throw new Error('status 是必需的')

    const { data, error } = await supabase
      .from('prompts')
      .insert([{
        user_id: prompt.user_id,
        keyword: prompt.keyword,
        optimized_prompt: prompt.optimized_prompt,
        category_id: prompt.category_id,
        status: prompt.status,
        error: prompt.error || null
      }])
      .select()
      .single()

    if (error) {
      console.error('Supabase 错误:', error)
      throw new Error(`数据库错误: ${error.message}`)
    }

    console.log('创建成功，返回数据:', data)
    return data
  } catch (error) {
    console.error('创建提示词失败:', error)
    if (error instanceof Error) {
      throw new Error(`创建提示词失败: ${error.message}`)
    }
    throw error
  }
}

// 更新提示词记录
export async function updatePrompt(id: string, updates: Partial<Prompt>): Promise<Prompt | null> {
  try {
    console.log('更新提示词，ID:', id, '更新数据:', updates)
    
    const { data, error } = await supabase
      .from('prompts')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('更新提示词失败:', error)
      throw error
    }

    console.log('更新提示词成功:', data)
    return data
  } catch (error) {
    console.error('更新提示词失败:', error)
    throw error
  }
} 