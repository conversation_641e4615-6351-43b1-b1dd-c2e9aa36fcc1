// models/category.ts
import { getSupabaseClient } from "@/models/db";

export interface Category {
  id: string;
  name: string;
  slug: string;
  description: string;
  icon?: string;
  seoTitle?: string;
  seoDescription?: string;
  parentId?: string;
  createdAt: Date;
  updatedAt: Date;
  sharesConfig?: {
    title?: string;
    items?: any[];
  };
  generatorConfig?: {
    default_prompt?: string;
    placeholder?: string;
    options?: any[];
  };
  faqConfig?: {
    title?: string;
    items?: {
      question: string;
      answer: string;
    }[];
  };
}

export async function getCategories(): Promise<Category[]> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("categories")
    .select("*")
    .order("created_at", { ascending: false });

  if (error) {
    console.error("Error fetching categories:", error);
    throw error;
  }

  return data || [];
}

export async function getCategoryById(id: string): Promise<Category | null> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("categories")
    .select("*")
    .eq("id", id)
    .single();

  if (error) {
    console.error(`Error fetching category with id ${id}:`, error);
    return null;
  }

  return data;
}

export async function getCategoryBySlug(slug: string): Promise<Category | null> {
  try {
    console.log(`Fetching category with slug ${slug} from Supabase...`);
    const supabase = getSupabaseClient();
    const { data, error } = await supabase
      .from("categories")
      .select("*")
      .eq("slug", slug)
      .single();

    if (error) {
      console.error(`Error fetching category with slug ${slug}:`, error);
      return null;
    }

    console.log(`Successfully fetched category with slug ${slug}`, data);
    
    // 添加默认配置数据，因为数据库可能没有这些字段
    return {
      ...data,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
      // 添加默认配置
      sharesConfig: data.sharesConfig || {
        title: "Latest Shares",
        items: []
      },
      generatorConfig: data.generatorConfig || {
        default_prompt: `Generate an image related to ${data.name}`,
        placeholder: "Describe what you want to generate...",
        options: []
      },
      faqConfig: data.faqConfig || {
        title: "常见问题与解答",
        items: [
          {
            question: `什么是${data.name}?`,
            answer: `${data.description || "This is a category description."}`
          }
        ]
      }
    };
  } catch (error) {
    console.error(`Exception in getCategoryBySlug for slug ${slug}:`, error);
    return null;
  }
}

export async function createCategory(category: Omit<Category, "id" | "created_at" | "updated_at">): Promise<Category | null> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("categories")
    .insert(category)
    .select()
    .single();

  if (error) {
    console.error("Error creating category:", error);
    return null;
  }

  return data;
}

export async function updateCategory(id: string, category: Partial<Category>): Promise<Category | null> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("categories")
    .update(category)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    console.error(`Error updating category with id ${id}:`, error);
    return null;
  }

  return data;
}

export async function deleteCategory(id: string): Promise<boolean> {
  const supabase = getSupabaseClient();
  const { error } = await supabase
    .from("categories")
    .delete()
    .eq("id", id);

  if (error) {
    console.error(`Error deleting category with id ${id}:`, error);
    return false;
  }

  return true;
}