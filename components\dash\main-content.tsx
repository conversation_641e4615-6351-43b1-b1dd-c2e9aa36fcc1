import { MoreVert<PERSON>, Send } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import Link from "next/link"

interface MainContentProps {
  theme?: "dark"
}

export function MainContent({ theme = "dark" }: MainContentProps) {
  return (
    <div className="flex-1 overflow-auto p-4 md:p-6 bg-gray-800 text-gray-100">
      {/* Breadcrumb */}
      <div className="flex items-center text-sm text-gray-400 mb-4 md:mb-6">
        <Link href="/" className="hover:text-gray-200 text-gray-400">
          Dashboard
        </Link>
        <svg className="mx-2 h-4 w-4" viewBox="0 0 16 16" fill="currentColor">
          <path
            fillRule="evenodd"
            d="M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z"
          />
        </svg>
        <span className="text-gray-200">Create a Social Media Post</span>
      </div>

      {/* Brand Identity Alert */}
      <div className="bg-gray-700 border-gray-600 rounded-lg p-4 mb-4 md:mb-6 transition-all hover:shadow-md">
        <div className="flex items-start">
          <div className="flex-shrink-0 mr-3">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="3" y="3" width="18" height="18" rx="2" stroke="#6941C6" strokeWidth="2" />
              <path d="M3 9L21 9" stroke="#6941C6" strokeWidth="2" />
              <path d="M9 21L9 9" stroke="#6941C6" strokeWidth="2" />
            </svg>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-100">Brand Identity Not Set</h3>
            <div className="mt-1 text-sm text-gray-300">
              Your brand identity is not set.{" "}
              <a href="#" className="text-purple-600 hover:text-purple-400 font-medium">
                Click here to update your brand content.
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Conversation Card */}
      <div className="bg-gray-700 border-gray-600 rounded-lg p-4 md:p-6 mb-4 md:mb-6 transition-all hover:shadow-md">
        <h2 className="text-lg md:text-xl font-semibold text-white mb-2">
          Start a Conversation with Agent "Create a Social Media Post"
        </h2>
        <p className="text-sm md:text-base text-gray-300 mb-4">
          Create engaging and personalized text posts for different social media platforms, tailored to specific
          audiences, themes, and brand guidelines.
        </p>
        <Button className="bg-purple-600 hover:bg-purple-700 text-white transition-colors">
          Start Conversation
        </Button>
      </div>

      {/* Content Suggestions */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4 md:mb-6">
        <Card className="bg-gray-700 border-gray-600 hover:bg-gray-600 transition-all">
          <CardContent className="p-4">
            <h3 className="font-medium text-white">The future of work:</h3>
            <p className="text-gray-300">AI and human collaboration</p>
          </CardContent>
        </Card>

        <Card className="bg-gray-700 border-gray-600 hover:bg-gray-600 transition-all">
          <CardContent className="p-4">
            <h3 className="font-medium text-white">5 eco-friendly habits</h3>
            <p className="text-gray-300">that save you money</p>
          </CardContent>
        </Card>

        <Card className="bg-gray-700 border-gray-600 hover:bg-gray-600 transition-all">
          <CardContent className="p-4">
            <h3 className="font-medium text-white">Why introverts make</h3>
            <p className="text-gray-300">great leaders</p>
          </CardContent>
        </Card>

        <Card className="bg-gray-700 border-gray-600 hover:bg-gray-600 transition-all">
          <CardContent className="p-4">
            <h3 className="font-medium text-white">The psychology behind</h3>
            <p className="text-gray-300">viral social media posts</p>
          </CardContent>
        </Card>
      </div>

      {/* Chat Input */}
      <div className="bg-gray-700 border-gray-600 rounded-lg p-4 sticky bottom-0 transition-all">
        <div className="flex items-center">
          <button className="p-2 rounded-full hover:bg-gray-600 mr-2">
            <MoreVertical className="h-5 w-5 text-gray-300" />
          </button>
          <input
            type="text"
            placeholder="Send a message..."
            className="flex-1 border-0 focus:ring-0 focus:outline-none text-sm bg-gray-700 text-white placeholder-gray-400"
          />
          <Button className="ml-2 rounded-full bg-gray-600 hover:bg-gray-500 text-white p-2 h-auto">
            <Send className="h-5 w-5" />
          </Button>
        </div>
        <div className="mt-2 text-xs text-center text-gray-400">Create a Social Media Post AI Agent</div>
      </div>
    </div>
  )
}
