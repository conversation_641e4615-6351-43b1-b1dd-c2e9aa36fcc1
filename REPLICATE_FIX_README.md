# Replicate 图片生成功能修复说明

## 修复内容

### 1. API 路由修复 (`/app/api/replicate/route.ts`)
- ✅ 修复了 Replicate API 集成
- ✅ 添加了用户积分检查
- ✅ 使用正确的 Flux 模型版本
- ✅ 改进了错误处理
- ✅ 添加了 R2 存储配置

### 2. 前端组件修复 (`/components/keling/ImageGenerator.tsx`)
- ✅ 修复了 API 响应处理
- ✅ 改进了状态管理
- ✅ 添加了更好的用户反馈

### 3. 数据库迁移 (`/data/migrations/add_metadata_to_prompts.sql`)
- ✅ 添加了 `metadata` 字段到 `prompts` 表
- ✅ 添加了必要的索引

### 4. 环境变量配置 (`.env.example`)
- ✅ 添加了 Replicate API 配置
- ✅ 添加了 Cloudflare R2 配置

## 配置步骤

### 1. 环境变量设置
在 `.env.local` 文件中添加以下配置：

```env
# Replicate API
REPLICATE_API_TOKEN=your_replicate_api_token

# Cloudflare R2
CLOUDFLARE_ACCOUNT_ID=your_account_id
CLOUDFLARE_ACCESS_KEY_ID=your_access_key
CLOUDFLARE_SECRET_ACCESS_KEY=your_secret_key
CLOUDFLARE_R2_BUCKET_NAME=your_bucket_name
NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_DOMAIN=https://your-domain.com

# 或者使用 R2 替代配置
R2_REGION=auto
R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
R2_ACCESS_KEY_ID=your_access_key
R2_SECRET_ACCESS_KEY=your_secret_key
R2_BUCKET_NAME=your_bucket_name
R2_PUBLIC_URL=https://your-domain.com
```

### 2. 数据库迁移
运行数据库迁移脚本：

```sql
-- 在 Supabase 控制台或数据库客户端中执行
ALTER TABLE prompts ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}';
CREATE INDEX IF NOT EXISTS idx_prompts_metadata ON prompts USING GIN (metadata);
CREATE INDEX IF NOT EXISTS idx_prompts_prediction_id ON prompts (prediction_id);
```

### 3. Replicate Webhook 配置
在 Replicate 控制台中设置 webhook URL：
```
https://your-domain.com/api/replicate/webhook
```

## 工作流程

1. **用户选择类别** → 从数据库获取该类别的提示词
2. **点击生成图片** → 调用 `/api/replicate` API
3. **API 处理**：
   - 检查用户积分
   - 创建/更新数据库记录
   - 调用 Replicate API 创建预测
   - 扣除积分
   - 返回预测ID
4. **前端轮询** → 检查数据库中的图片状态
5. **Webhook 处理**：
   - Replicate 完成后调用 webhook
   - 下载图片并上传到 R2
   - 更新数据库记录

## 测试

### 1. 运行测试脚本
```bash
node debug/test-replicate-api.js
```

### 2. 手动测试
1. 启动开发服务器：`npm run dev`
2. API 测试页面：`http://localhost:3000/test-replicate`
3. 完整功能测试：`http://localhost:3000/jihua/tupian/generate`
4. 选择类别并点击生成图片

## 故障排除

### 常见问题

1. **积分不足**
   - 检查用户积分余额
   - 确保积分服务正常工作

2. **Replicate API 错误**
   - 检查 `REPLICATE_API_TOKEN` 是否正确
   - 确认 API 配额是否充足

3. **R2 上传失败**
   - 检查 R2 配置是否正确
   - 确认存储桶权限设置

4. **Webhook 不工作**
   - 检查 webhook URL 配置
   - 确认服务器可以接收外部请求

### 日志检查
- 查看浏览器控制台
- 检查服务器日志
- 查看 Replicate 控制台日志

## 下一步优化

1. 添加图片生成队列管理
2. 实现批量生成功能
3. 添加图片编辑功能
4. 优化轮询机制
5. 添加更多模型选择
