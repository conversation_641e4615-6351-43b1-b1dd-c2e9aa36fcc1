"use client"

import { useState, useEffect, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Loader2 } from "lucide-react"
import { toast } from "sonner"
import { useSession } from "next-auth/react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { supabase } from '@/lib/supabase'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

interface Prompt {
  id: string
  keyword: string
  optimized_prompt: string
  category_id: string
  created_at: string
  image_url?: string
  image_error?: string
  metadata?: {
    predictionId?: string
  }
}

interface Category {
  id: string
  name: string
}

const ImageGenerator = () => {
  const [prompts, setPrompts] = useState<Prompt[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [selectedCategory, setSelectedCategory] = useState("")
  const [isLoadingCategories, setIsLoadingCategories] = useState(true)
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatingPromptId, setGeneratingPromptId] = useState<string | null>(null)
  const [generatedImages, setGeneratedImages] = useState<{[key: string]: string}>({})
  const { data: session, status } = useSession()

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      setIsLoadingCategories(true)
      try {
        const response = await fetch("/api/categories")
        if (!response.ok) throw new Error("获取类别失败")
        const data = await response.json()
        setCategories(data)
        if (data.length > 0) setSelectedCategory(data[0].id)
      } catch (error) {
        console.error("获取类别失败:", error)
        toast.error(error instanceof Error ? error.message : "获取类别失败")
      } finally {
        setIsLoadingCategories(false)
      }
    }
    fetchCategories()
  }, [])

  // Fetch prompts when category changes
  useEffect(() => {
    const fetchPrompts = async () => {
      if (!selectedCategory) return
      
      try {
        const { data, error } = await supabase
          .from('prompts')
          .select('*')
          .eq('category_id', selectedCategory)
          .order('created_at', { ascending: false })

        if (error) throw error
        setPrompts(data || [])
      } catch (error) {
        console.error("获取提示词失败:", error)
        toast.error("获取提示词失败")
      }
    }

    fetchPrompts()
  }, [selectedCategory])

  // 统一的轮询函数
  const pollPromptStatus = useCallback(async (promptId: string) => {
    try {
      const { data: promptData, error: fetchError } = await supabase
        .from('prompts')
        .select('*')
        .eq('id', promptId)
        .single();

      if (fetchError) {
        console.error('Error fetching prompt status:', fetchError);
        return false;
      }

      // 检查是否有图片URL
      if (promptData.image_url) {
        // 更新本地状态
        setGeneratedImages(prev => ({
          ...prev,
          [promptId]: promptData.image_url
        }));
        
        // 更新prompts列表中的状态
        setPrompts(prev => prev.map(p => 
          p.id === promptId 
            ? { ...p, image_url: promptData.image_url }
            : p
        ));
        
        setGeneratingPromptId(null);
        toast.success('图片生成成功');
        return true;
      } else if (promptData.image_error) {
        // 更新prompts列表中的状态
        setPrompts(prev => prev.map(p => 
          p.id === promptId 
            ? { ...p, image_error: promptData.image_error }
            : p
        ));
        
        setGeneratingPromptId(null);
        toast.error(`生成失败: ${promptData.image_error || '未知错误'}`);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error polling prompt status:', error);
      return false;
    }
  }, []);

  // 统一的轮询效果
  useEffect(() => {
    if (!generatingPromptId) return;

    const pollInterval = setInterval(async () => {
      const isComplete = await pollPromptStatus(generatingPromptId);
      if (isComplete) {
        clearInterval(pollInterval);
      }
    }, 2000);

    // 2分钟后超时
    const timeout = setTimeout(() => {
      clearInterval(pollInterval);
      setGeneratingPromptId(null);
      toast.error('图片生成超时，请稍后重试');
    }, 120000);

    return () => {
      clearInterval(pollInterval);
      clearTimeout(timeout);
    };
  }, [generatingPromptId, pollPromptStatus]);

  const generateImage = async (prompt: Prompt) => {
    if (isGenerating) return;
    
    setIsGenerating(true);
    setGeneratingPromptId(prompt.id);
    
    try {
      // 清除之前的错误信息
      const { error: updateError } = await supabase
        .from('prompts')
        .update({ 
          image_error: null
        })
        .eq('id', prompt.id);

      if (updateError) throw updateError;
      
      const response = await fetch("/api/replicate", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          prompt: prompt.optimized_prompt,
          categoryId: prompt.category_id,
          width: 768,
          height: 1152,
          count: 1,
          aspect_ratio: "3:4",
          prompt_id: prompt.id
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        
        // 更新失败状态
        await supabase
          .from('prompts')
          .update({ 
            image_error: errorText
          })
          .eq('id', prompt.id);

        // 更新本地状态
        setPrompts(prev => prev.map(p => 
          p.id === prompt.id 
            ? { ...p, image_error: errorText }
            : p
        ));
        
        throw new Error(`生成图片失败: ${errorText}`);
      }

      const data = await response.json();
      
      // 更新数据库中的 predictionId
      if (data.predictionId) {
        await supabase
          .from('prompts')
          .update({ 
            metadata: {
              ...prompt.metadata,
              predictionId: data.predictionId
            }
          })
          .eq('id', prompt.id);
      }

      // 开始轮询状态
      await pollPromptStatus(prompt.id);
      
    } catch (error) {
      console.error('Image generation error:', error);
      toast.error(`生成图片失败: ${error instanceof Error ? error.message : '未知错误'}`);
      setGeneratingPromptId(null);
    } finally {
      setIsGenerating(false);
    }
  }

  if (status === "loading") {
    return (
      <Card className="w-full bg-gray-800/50 backdrop-blur-sm border-gray-700/50">
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="h-6 w-6 animate-spin text-white" />
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full bg-gray-800/50 backdrop-blur-sm border-gray-700/50">
      <CardHeader>
        <CardTitle className="text-xl font-bold text-white">从提示词生成图片</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Category Selection */}
          <div className="space-y-2">
            <Label className="text-gray-300">选择类别</Label>
            <Select 
              value={selectedCategory} 
              onValueChange={setSelectedCategory}
              disabled={isLoadingCategories || isGenerating}
            >
              <SelectTrigger className="bg-gray-900/50 border-gray-700 text-gray-300">
                <SelectValue placeholder={isLoadingCategories ? "加载中..." : "选择图片类别"} />
              </SelectTrigger>
              <SelectContent className="bg-gray-900 border-gray-700">
                {categories.map((category) => (
                  <SelectItem 
                    key={category.id} 
                    value={category.id}
                    className="text-gray-300 hover:bg-gray-800"
                  >
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Prompts Table */}
          {prompts.length > 0 && (
            <div className="rounded-md border border-gray-700/50 overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="hover:bg-gray-800/50">
                    <TableHead className="w-[120px] text-gray-300">关键词</TableHead>
                    <TableHead className="text-gray-300">提示词</TableHead>
                    <TableHead className="w-[100px] text-gray-300">状态</TableHead>
                    <TableHead className="w-[100px] text-gray-300">操作</TableHead>
                    <TableHead className="w-[120px] text-gray-300">生成结果</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {prompts.map((prompt) => (
                    <TableRow key={prompt.id} className="hover:bg-gray-800/50 h-[80px]">
                      <TableCell className="font-medium text-emerald-400">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger>
                              <div className="truncate max-w-[100px]">
                                {prompt.keyword}
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className="text-sm">{prompt.keyword}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </TableCell>
                      <TableCell>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger className="text-left">
                              <div className="text-gray-300 truncate max-w-[500px]">
                                {prompt.optimized_prompt}
                              </div>
                            </TooltipTrigger>
                            <TooltipContent className="max-w-[600px]">
                              <p className="text-sm">{prompt.optimized_prompt}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-sm ${
                          prompt.image_url
                            ? 'bg-green-700 text-green-200'
                            : prompt.image_error
                            ? 'bg-red-700 text-red-200'
                            : generatingPromptId === prompt.id
                            ? 'bg-yellow-700 text-yellow-200'
                            : 'bg-gray-700 text-gray-300'
                        }`}>
                          {prompt.image_url
                            ? '已生成' 
                            : prompt.image_error
                            ? '生成失败'
                            : generatingPromptId === prompt.id
                            ? '生成中'
                            : '未生成'}
                        </span>
                      </TableCell>
                      <TableCell>
                        <Button
                          size="sm"
                          className="bg-emerald-600 hover:bg-emerald-700 text-white"
                          onClick={() => generateImage(prompt)}
                          disabled={isGenerating || generatingPromptId === prompt.id}
                        >
                          {generatingPromptId === prompt.id ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              生成中
                            </>
                          ) : (
                            prompt.image_url ? '已生成' : '生成图片'
                          )}
                        </Button>
                      </TableCell>
                      <TableCell>
                        {(generatedImages[prompt.id] || prompt.image_url) && (
                          <div 
                            className="relative cursor-pointer group"
                            onClick={() => window.open(generatedImages[prompt.id] || prompt.image_url, '_blank')}
                          >
                            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                              <span className="text-white text-sm">点击查看大图</span>
                            </div>
                            <img 
                              src={generatedImages[prompt.id] || prompt.image_url} 
                              alt="Generated" 
                              className="w-20 h-20 object-cover rounded-lg shadow-lg"
                            />
                          </div>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {prompts.length === 0 && !isLoadingCategories && (
            <div className="text-center text-gray-400 py-8 bg-gray-900/50 rounded-lg">
              该类别下暂无提示词
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

// Add custom scrollbar styles
const styles = `
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }
  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
  }
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
  }
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
  }
`

export default function ImageGeneratorWithStyles() {
  return (
    <>
      <style>{styles}</style>
      <ImageGenerator />
    </>
  )
} 