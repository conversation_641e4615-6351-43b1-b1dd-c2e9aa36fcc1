"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { <PERSON>ader2, <PERSON><PERSON><PERSON> } from "lucide-react"
import { toast } from "sonner"
import { useSession } from "next-auth/react"
import Image from "next/image"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface AutoGenerateProps {
  onGenerate?: (images: string[]) => void
}

interface Category {
  id: string
  name: string
}

const AutoGenerate = ({ onGenerate }: AutoGenerateProps) => {
  const [keyword, setKeyword] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)
  const [generatedImages, setGeneratedImages] = useState<string[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [selectedCategory, setSelectedCategory] = useState("")
  const [isLoadingCategories, setIsLoadingCategories] = useState(true)
  const { data: session } = useSession()

  // 获取类别列表
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch("/api/categories")
        if (!response.ok) {
          throw new Error("获取类别失败")
        }
        const data = await response.json()
        setCategories(data)
        // 如果有类别，默认选择第一个
        if (data.length > 0) {
          setSelectedCategory(data[0].id)
        }
      } catch (error) {
        console.error("获取类别失败:", error)
        toast.error("获取类别失败")
      } finally {
        setIsLoadingCategories(false)
      }
    }

    fetchCategories()
  }, [])

  // 处理关键词输入
  const handleKeywordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setKeyword(e.target.value)
  }

  // 自动优化提示词
  const optimizePrompt = async (keyword: string) => {
    try {
      const response = await fetch("/api/zhiti", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          instruction: keyword,
        }),
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error("提示词优化失败:", errorText)
        throw new Error(`提示词优化失败: ${errorText}`)
      }

      const data = await response.json()
      const prompts = data.content.split("\n").filter((p: string) => p.trim())
      
      if (prompts.length > 0) {
        return prompts[0] // 返回第一个优化后的提示词
      }
      return keyword
    } catch (error) {
      console.error("优化提示词失败:", error)
      toast.error("提示词优化失败，使用原始关键词")
      return keyword
    }
  }

  // 提取图片URL的辅助函数
  const extractImageUrls = (data: any): string[] => {
    if (!data) return [];
    
    // 处理不同的数据格式
    if (data.r2Urls && Array.isArray(data.r2Urls) && data.r2Urls.length > 0) {
      return data.r2Urls;
    }
    
    if (data.r2Url) {
      return [data.r2Url];
    }
    
    if (data.images && Array.isArray(data.images) && data.images.length > 0) {
      return data.images;
    }
    
    if (data.imageUrls && Array.isArray(data.imageUrls)) {
      return data.imageUrls;
    }
    
    if (data.imageUrl) {
      return [data.imageUrl];
    }
    
    if (Array.isArray(data) && data.length > 0) {
      return data;
    }
    
    return [];
  }

  // 生成图片
  const generateImages = async (prompt: string) => {
    try {
      const response = await fetch("/api/pic/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          prompt: prompt,
          width: 1024,
          height: 1024,
          count: 4,
          model: "flux.schnell",
          seed: Math.floor(Math.random() * 1000000),
          categoryId: selectedCategory,
          useCredits: true,
          creditCost: 3
        }),
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error("图片生成失败:", errorText)
        throw new Error(`图片生成失败: ${errorText}`)
      }

      const data = await response.json()
      return extractImageUrls(data)
    } catch (error) {
      console.error("生成图片失败:", error)
      throw error
    }
  }

  // 处理自动生成流程
  const handleAutoGenerate = async () => {
    if (!keyword.trim()) {
      toast.error("请输入关键词")
      return
    }

    if (!session) {
      toast.error("请先登录")
      return
    }

    if (!selectedCategory) {
      toast.error("请选择图片类别")
      return
    }

    setIsProcessing(true)
    setGeneratedImages([])

    try {
      // 1. 优化提示词
      toast.info("正在优化提示词...")
      const optimizedPrompt = await optimizePrompt(keyword)
      console.log("优化后的提示词:", optimizedPrompt)
      
      // 2. 生成图片
      toast.info("正在生成图片...")
      const images = await generateImages(optimizedPrompt)
      
      if (images && images.length > 0) {
        setGeneratedImages(images)
        onGenerate?.(images)
        toast.success("图片生成成功!")
      } else {
        throw new Error("未生成任何图片")
      }
    } catch (error) {
      console.error("自动生成流程失败:", error)
      toast.error(error instanceof Error ? error.message : "生成过程出错")
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <Card className="w-full bg-gray-900 border-gray-800">
      <CardHeader>
        <CardTitle className="text-xl font-bold text-white">自动生成</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* 关键词输入 */}
          <div className="space-y-2">
            <Label htmlFor="keyword" className="text-gray-300">输入关键词</Label>
            <div className="flex gap-2">
              <Input
                id="keyword"
                value={keyword}
                onChange={handleKeywordChange}
                placeholder="输入关键词，自动生成图片..."
                className="bg-gray-800 border-gray-700 text-gray-300"
              />
              <Button
                onClick={handleAutoGenerate}
                disabled={isProcessing || !keyword.trim() || isLoadingCategories}
                className="bg-emerald-600 hover:bg-emerald-700"
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    生成中...
                  </>
                ) : (
                  <>
                    <Sparkles className="mr-2 h-4 w-4" />
                    开始生成
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* 类别选择 */}
          <div className="space-y-2">
            <Label className="text-gray-300">选择类别</Label>
            <Select 
              value={selectedCategory} 
              onValueChange={setSelectedCategory}
              disabled={isLoadingCategories}
            >
              <SelectTrigger className="bg-gray-800 border-gray-700 text-gray-300">
                <SelectValue placeholder={isLoadingCategories ? "加载中..." : "选择图片类别"} />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-700">
                {categories.map((category) => (
                  <SelectItem 
                    key={category.id} 
                    value={category.id}
                    className="text-gray-300 hover:bg-gray-700"
                  >
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 生成结果展示 */}
          {generatedImages.length > 0 && (
            <div className="mt-6">
              <h3 className="text-lg font-semibold text-white mb-4">生成结果</h3>
              <div className="grid grid-cols-2 gap-4">
                {generatedImages.map((imageUrl, index) => (
                  <div key={index} className="relative aspect-square rounded-lg overflow-hidden group">
                    <Image
                      src={imageUrl}
                      alt={`Generated image ${index + 1}`}
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-opacity duration-300 flex items-center justify-center">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                        onClick={() => window.open(imageUrl, '_blank')}
                      >
                        查看大图
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export default AutoGenerate 