"use client"

import { useState, useEffect } from "react"
import { Heart, MessageSquare, Share } from "lucide-react"
import { Card } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { toast } from "sonner"
import { ImageHistory as ImageHistoryType } from "@/lib/imageHistory"

interface ImageAuthor {
  id: string
  username: string
  userHandle: string
  avatar: string
  prompt: string
  images: string[]
  likes: number
  comments: number
  timestamp: string
}

interface ImageHistoryProps {
  userId?: string
  initialImages?: ImageHistoryType[]
}

export default function ImageHistory({ userId, initialImages }: ImageHistoryProps) {
  // 如果有初始数据，直接使用格式化函数处理
  const initialAuthors = initialImages ? 
    initialImages.map((image) => ({
      id: image.id,
      username: "可灵AI",
      userHandle: "@keling_ai",
      avatar: "/placeholder.svg?height=40&width=40",
      prompt: image.prompt,
      images: [image.image_url],
      likes: 0,
      comments: 0,
      timestamp: new Date(image.created_at).toLocaleString('zh-CN', {
        hour: 'numeric',
        minute: 'numeric',
        hour12: false
      })
    })) : [];
  
  const [authors, setAuthors] = useState<ImageAuthor[]>(initialAuthors)
  const [isLoading, setIsLoading] = useState(!initialImages || initialImages.length === 0)

  const formatImagesData = (images: ImageHistoryType[]) => {
    return images.map((image) => ({
      id: image.id,
      username: "可灵AI",
      userHandle: "@keling_ai",
      avatar: "/placeholder.svg?height=40&width=40",
      prompt: image.prompt,
      images: [image.image_url], // 只显示一张图片
      likes: 0,
      comments: 0,
      timestamp: new Date(image.created_at).toLocaleString('zh-CN', {
        hour: 'numeric',
        minute: 'numeric',
        hour12: false
      })
    }))
  }

  const fetchImages = async () => {
    // 如果有初始数据并且不是加载状态，不需要再次获取
    if (initialImages && initialImages.length > 0 && !isLoading) {
      return
    }
    
    try {
      setIsLoading(true)
      const apiUrl = userId ? `/api/pic/history?userId=${userId}` : "/api/pic/history"
      const response = await fetch(apiUrl)
      if (!response.ok) {
        throw new Error("获取历史记录失败")
      }
      const data = await response.json()
      
      setAuthors(formatImagesData(data))
    } catch (error) {
      console.error("获取历史记录失败:", error)
      toast.error("获取历史记录失败")
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    // 如果没有初始数据，才需要从API获取
    if (!initialImages || initialImages.length === 0) {
      fetchImages()
    }
    // 绑定刷新方法到window对象
    window.refreshImageHistory = fetchImages
  }, [initialImages, userId])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-500"></div>
      </div>
    )
  }

  if (authors.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-gray-400">
        <p>暂无生成的图片</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {authors.map((author) => (
        <Card
          key={author.id}
          className="bg-gray-900 border-gray-800 overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-shadow"
        >
          {/* Author Header */}
          <div className="p-4 flex items-center justify-between border-b border-gray-800 bg-gray-900">
            <div className="flex items-center">
              <div className="w-10 h-10 rounded-full bg-gray-700 overflow-hidden mr-3 border-2 border-emerald-500/30">
                <img
                  src={author.avatar || "/placeholder.svg"}
                  alt={author.username}
                  className="w-full h-full object-cover"
                />
              </div>
              <div>
                <div className="font-semibold text-white">{author.username}</div>
                <div className="text-sm text-emerald-300">{author.userHandle}</div>
              </div>
            </div>
            <div className="text-xs text-gray-400 bg-gray-800 px-2 py-1 rounded-full">{author.timestamp}</div>
          </div>

          {/* Prompt */}
          <div className="px-4 py-3 border-b border-gray-800 bg-gray-900/70">
            <p className="text-sm text-gray-300">{author.prompt}</p>
          </div>

          {/* Images Grid */}
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 p-2 bg-gray-950">
            {author.images.map((imageUrl, index) => (
              <div key={index} className="relative aspect-[3/4] group overflow-hidden rounded-lg">
                <img
                  src={imageUrl || "/placeholder.svg"}
                  alt={`Generated by ${author.username}`}
                  className="w-full h-full object-cover rounded-lg"
                />
                <div className="absolute bottom-2 right-2 text-xs bg-black bg-opacity-70 px-2 py-1 rounded-full text-emerald-400 font-medium">
                  可灵AI
                </div>
              </div>
            ))}
          </div>

          {/* Actions */}
          <div className="p-4 flex items-center justify-between border-t border-gray-800 bg-gray-900/80">
            <div className="flex items-center space-x-6">
              <button className="flex items-center text-gray-400 hover:text-rose-500 transition-colors">
                <Heart className="h-5 w-5 mr-1" />
                <span className="text-sm">{author.likes}</span>
              </button>
              <button className="flex items-center text-gray-400 hover:text-cyan-500 transition-colors">
                <MessageSquare className="h-5 w-5 mr-1" />
                <span className="text-sm">{author.comments}</span>
              </button>
              <button className="flex items-center text-gray-400 hover:text-emerald-500 transition-colors">
                <Share className="h-5 w-5" />
              </button>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="text-xs bg-gray-800 border-gray-700 text-emerald-300 hover:bg-gray-700 hover:text-emerald-200"
            >
              查看更多
            </Button>
          </div>
        </Card>
      ))}
    </div>
  )
}

