"use client"

import { ChevronLeft, ChevronRight, Globe, Menu, LogOut } from "lucide-react"
import { Avatar } from "@/components/ui/avatar"
import { AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import { signIn, signOut } from "next-auth/react"
import { useSession } from "next-auth/react"
import Link from "next/link"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface TopNavbarProps {
  onToggleSidebar?: () => void
  isMobile?: boolean
  sidebarOpen?: boolean
}

export function TopNavbar({ onToggleSidebar, isMobile, sidebarOpen }: TopNavbarProps) {
  const { data: session } = useSession()
  const user = session?.user

  return (
    <div className="bg-gray-800 text-white border-b border-gray-700">
      {/* Promotion Banner */}
      <div className="bg-gradient-to-r from-[#9E77ED] to-[#FDB022] py-2 px-4 flex items-center justify-between text-white">
        <button className="text-white/80 hover:text-white" aria-label="Previous promotion">
          <ChevronLeft className="h-5 w-5" />
        </button>

        <div className="flex items-center">
          <span className="bg-white/20 rounded-full p-1 mr-2">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M20 12L4 4L6 12L4 20L20 12Z" fill="currentColor" />
            </svg>
          </span>
          <span className="font-medium text-xs sm:text-sm md:text-base truncate max-w-[250px] sm:max-w-md md:max-w-xl">
            Final Lifetime Offer: Last Chance! - Lifetime access at 79€ - Only 57 licenses left before we switch to
            subscription model. Don't miss it before it's gone! 🚀
          </span>
        </div>

        <button className="text-white/80 hover:text-white" aria-label="Next promotion">
          <ChevronRight className="h-5 w-5" />
        </button>
      </div>

      {/* Main Navbar */}
      <div className="flex items-center justify-between p-4">
        {/* Mobile menu button */}
        {isMobile && (
          <Button 
            variant="outline" 
            size="icon" 
            onClick={onToggleSidebar} 
            className="mr-2 border-gray-700 bg-gray-700 hover:bg-gray-600 hover:text-white"
            aria-label={sidebarOpen ? "Close sidebar" : "Open sidebar"}
            aria-expanded={sidebarOpen}
          >
            <Menu className="h-5 w-5 text-gray-300" />
          </Button>
        )}

        <div className="flex items-center space-x-4 flex-1 md:w-1/3">
          <div className="relative w-full max-w-md">
            <div className="flex items-center border rounded-md px-3 py-2 border-gray-700 bg-gray-700/50">
              <Globe className="h-5 w-5 text-gray-400 mr-2" />
              <span className="text-sm font-medium truncate text-gray-200">Social Media Network</span>
              <svg className="ml-auto h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                <path
                  fillRule="evenodd"
                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2 md:space-x-4">
          <div className="hidden sm:flex items-center">
            
            <span className="text-sm font-medium text-gray-200">Upgrade</span>
          </div>

          {user ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <div className="flex items-center cursor-pointer">
                  <Avatar className="h-8 w-8 bg-green-500">
                    <AvatarFallback>{user.name ? user.name.substring(0, 2).toUpperCase() : 'U'}</AvatarFallback>
                    {user.image && <AvatarImage src={user.image} alt={user.name || 'User'} />}
                  </Avatar>
                  <div className="ml-2 hidden sm:block">
                    <span className="text-sm font-medium text-gray-200">
                      {user.name || user.email}
                    </span>
                  </div>
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="bg-gray-800 border-gray-700">
                <div className="px-3 py-2 text-sm">
                  <div className="font-medium text-gray-200">{user.name}</div>
                  <div className="text-xs text-gray-400">{user.email}</div>
                </div>
                <DropdownMenuSeparator className="bg-gray-700" />
                <DropdownMenuItem asChild className="text-gray-200 focus:bg-gray-700 focus:text-white">
                  <Link href="/profile">Profile</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild className="text-gray-200 focus:bg-gray-700 focus:text-white">
                  <Link href="/settings">Settings</Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator className="bg-gray-700" />
                <DropdownMenuItem 
                  className="text-red-500 focus:bg-gray-700 focus:text-red-500 cursor-pointer"
                  onClick={() => signOut()}
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <Button
              variant="outline"
              size="sm"
              onClick={() => signIn()}
              className="border-gray-700 text-gray-200 hover:bg-gray-700"
            >
              Sign In
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}
