import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface ModelSelectionProps {
  selectedModel: string;
  setSelectedModel: (value: string) => void;
}

const ModelSelection = ({ selectedModel, setSelectedModel }: ModelSelectionProps) => {
  return (
    <div className="mt-4 flex flex-wrap gap-2">
      <span className="text-sm text-gray-400">Model Selection:</span>
      <div className="w-full mt-1">
        <Select
          value={selectedModel}
          onValueChange={setSelectedModel}
        >
          <SelectTrigger className="w-full bg-gray-800 border-gray-700 text-emerald-300 h-12">
            <SelectValue placeholder="Select model" />
          </SelectTrigger>
          <SelectContent className="bg-gray-900 border-gray-700">
            <SelectItem value="flux.schnell" className="hover:bg-gray-800 data-[state=checked]:justify-start">
              <div className="flex items-center w-full py-2">
                <svg className="h-6 w-6 mr-3 flex-shrink-0" viewBox="0 0 24 24" fill="#4285F4">
                  <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
                  <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853" />
                  <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05" />
                  <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335" />
                </svg>
                <div className="flex flex-col justify-start text-left">
                  <span className="font-medium">FLUX Schnell</span>
                  <span className="text-xs text-gray-400">Fast generation, good for portraits</span>
                </div>
              </div>
            </SelectItem>
            <SelectItem value="flux.creative" className="hover:bg-gray-800 data-[state=checked]:justify-start">
              <div className="flex items-center w-full py-2">
                <svg className="h-6 w-6 mr-3 flex-shrink-0" viewBox="0 0 24 24" fill="#1DA1F2">
                  <path d="M23.643 4.937c-.835.37-1.732.62-2.675.733.962-.576 1.7-1.49 2.048-2.578-.9.534-1.897.922-2.958 1.13-.85-.904-2.06-1.47-3.4-1.47-2.572 0-4.658 2.086-4.658 4.66 0 .364.042.718.12 1.06-3.873-.195-7.304-2.05-9.602-4.868-.4.69-.63 1.49-.63 2.342 0 1.616.823 3.043 2.072 3.878-.764-.025-1.482-.234-2.11-.583v.06c0 2.257 1.605 4.14 3.737 4.568-.392.106-.803.162-1.227.162-.3 0-.593-.028-.877-.082.593 1.85 2.313 3.198 4.352 3.234-1.595 1.25-3.604 1.995-5.786 1.995-.376 0-.747-.022-1.112-.065 2.062 1.323 4.51 2.093 7.14 2.093 8.57 0 13.255-7.098 13.255-13.254 0-.2-.005-.402-.014-.602.91-.658 1.7-1.477 2.323-2.41z" />
                </svg>
                <div className="flex flex-col justify-start text-left">
                  <span className="font-medium">FLUX Creative</span>
                  <span className="text-xs text-gray-400">Highly creative and artistic outputs</span>
                </div>
              </div>
            </SelectItem>
            <SelectItem value="flux.realistic" className="hover:bg-gray-800 data-[state=checked]:justify-start">
              <div className="flex items-center w-full py-2">
                <svg className="h-6 w-6 mr-3 flex-shrink-0" viewBox="0 0 24 24" fill="#4285F4">
                  <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
                  <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853" />
                  <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05" />
                  <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335" />
                </svg>
                <div className="flex flex-col justify-start text-left">
                  <span className="font-medium">FLUX Realistic</span>
                  <span className="text-xs text-gray-400">Photorealistic images and environments</span>
                </div>
              </div>
            </SelectItem>
            <SelectItem value="flux.anime" className="hover:bg-gray-800 data-[state=checked]:justify-start">
              <div className="flex items-center w-full py-2">
                <svg className="h-6 w-6 mr-3 flex-shrink-0" viewBox="0 0 24 24" fill="#1DA1F2">
                  <path d="M23.643 4.937c-.835.37-1.732.62-2.675.733.962-.576 1.7-1.49 2.048-2.578-.9.534-1.897.922-2.958 1.13-.85-.904-2.06-1.47-3.4-1.47-2.572 0-4.658 2.086-4.658 4.66 0 .364.042.718.12 1.06-3.873-.195-7.304-2.05-9.602-4.868-.4.69-.63 1.49-.63 2.342 0 1.616.823 3.043 2.072 3.878-.764-.025-1.482-.234-2.11-.583v.06c0 2.257 1.605 4.14 3.737 4.568-.392.106-.803.162-1.227.162-.3 0-.593-.028-.877-.082.593 1.85 2.313 3.198 4.352 3.234-1.595 1.25-3.604 1.995-5.786 1.995-.376 0-.747-.022-1.112-.065 2.062 1.323 4.51 2.093 7.14 2.093 8.57 0 13.255-7.098 13.255-13.254 0-.2-.005-.402-.014-.602.91-.658 1.7-1.477 2.323-2.41z" />
                </svg>
                <div className="flex flex-col justify-start text-left">
                  <span className="font-medium">FLUX Anime</span>
                  <span className="text-xs text-gray-400">Japanese anime and manga style</span>
                </div>
              </div>
            </SelectItem>
            <SelectItem value="flux.anime" className="hover:bg-gray-800 data-[state=checked]:justify-start">
              <div className="flex items-center w-full py-2">
                <svg className="h-6 w-6 mr-3 flex-shrink-0" viewBox="0 0 24 24" fill="#1DA1F2">
                  <path d="M23.643 4.937c-.835.37-1.732.62-2.675.733.962-.576 1.7-1.49 2.048-2.578-.9.534-1.897.922-2.958 1.13-.85-.904-2.06-1.47-3.4-1.47-2.572 0-4.658 2.086-4.658 4.66 0 .364.042.718.12 1.06-3.873-.195-7.304-2.05-9.602-4.868-.4.69-.63 1.49-.63 2.342 0 1.616.823 3.043 2.072 3.878-.764-.025-1.482-.234-2.11-.583v.06c0 2.257 1.605 4.14 3.737 4.568-.392.106-.803.162-1.227.162-.3 0-.593-.028-.877-.082.593 1.85 2.313 3.198 4.352 3.234-1.595 1.25-3.604 1.995-5.786 1.995-.376 0-.747-.022-1.112-.065 2.062 1.323 4.51 2.093 7.14 2.093 8.57 0 13.255-7.098 13.255-13.254 0-.2-.005-.402-.014-.602.91-.658 1.7-1.477 2.323-2.41z" />
                </svg>
                <div className="flex flex-col justify-start text-left">
                  <span className="font-medium">FLUX Anime</span>
                  <span className="text-xs text-gray-400">Japanese anime and manga style</span>
                </div>
              </div>
            </SelectItem>
            <SelectItem value="flux.anime" className="hover:bg-gray-800 data-[state=checked]:justify-start">
              <div className="flex items-center w-full py-2">
                <svg className="h-6 w-6 mr-3 flex-shrink-0" viewBox="0 0 24 24" fill="#1DA1F2">
                  <path d="M23.643 4.937c-.835.37-1.732.62-2.675.733.962-.576 1.7-1.49 2.048-2.578-.9.534-1.897.922-2.958 1.13-.85-.904-2.06-1.47-3.4-1.47-2.572 0-4.658 2.086-4.658 4.66 0 .364.042.718.12 1.06-3.873-.195-7.304-2.05-9.602-4.868-.4.69-.63 1.49-.63 2.342 0 1.616.823 3.043 2.072 3.878-.764-.025-1.482-.234-2.11-.583v.06c0 2.257 1.605 4.14 3.737 4.568-.392.106-.803.162-1.227.162-.3 0-.593-.028-.877-.082.593 1.85 2.313 3.198 4.352 3.234-1.595 1.25-3.604 1.995-5.786 1.995-.376 0-.747-.022-1.112-.065 2.062 1.323 4.51 2.093 7.14 2.093 8.57 0 13.255-7.098 13.255-13.254 0-.2-.005-.402-.014-.602.91-.658 1.7-1.477 2.323-2.41z" />
                </svg>
                <div className="flex flex-col justify-start text-left">
                  <span className="font-medium">FLUX Anime</span>
                  <span className="text-xs text-gray-400">Japanese anime and manga style</span>
                </div>
              </div>
            </SelectItem>
            <SelectItem value="flux.anime" className="hover:bg-gray-800 data-[state=checked]:justify-start">
              <div className="flex items-center w-full py-2">
                <svg className="h-6 w-6 mr-3 flex-shrink-0" viewBox="0 0 24 24" fill="#1DA1F2">
                  <path d="M23.643 4.937c-.835.37-1.732.62-2.675.733.962-.576 1.7-1.49 2.048-2.578-.9.534-1.897.922-2.958 1.13-.85-.904-2.06-1.47-3.4-1.47-2.572 0-4.658 2.086-4.658 4.66 0 .364.042.718.12 1.06-3.873-.195-7.304-2.05-9.602-4.868-.4.69-.63 1.49-.63 2.342 0 1.616.823 3.043 2.072 3.878-.764-.025-1.482-.234-2.11-.583v.06c0 2.257 1.605 4.14 3.737 4.568-.392.106-.803.162-1.227.162-.3 0-.593-.028-.877-.082.593 1.85 2.313 3.198 4.352 3.234-1.595 1.25-3.604 1.995-5.786 1.995-.376 0-.747-.022-1.112-.065 2.062 1.323 4.51 2.093 7.14 2.093 8.57 0 13.255-7.098 13.255-13.254 0-.2-.005-.402-.014-.602.91-.658 1.7-1.477 2.323-2.41z" />
                </svg>
                <div className="flex flex-col justify-start text-left">
                  <span className="font-medium">FLUX Anime</span>
                  <span className="text-xs text-gray-400">Japanese anime and manga style</span>
                </div>
              </div>
            </SelectItem>
            <SelectItem value="flux.anime" className="hover:bg-gray-800 data-[state=checked]:justify-start">
              <div className="flex items-center w-full py-2">
                <svg className="h-6 w-6 mr-3 flex-shrink-0" viewBox="0 0 24 24" fill="#1DA1F2">
                  <path d="M23.643 4.937c-.835.37-1.732.62-2.675.733.962-.576 1.7-1.49 2.048-2.578-.9.534-1.897.922-2.958 1.13-.85-.904-2.06-1.47-3.4-1.47-2.572 0-4.658 2.086-4.658 4.66 0 .364.042.718.12 1.06-3.873-.195-7.304-2.05-9.602-4.868-.4.69-.63 1.49-.63 2.342 0 1.616.823 3.043 2.072 3.878-.764-.025-1.482-.234-2.11-.583v.06c0 2.257 1.605 4.14 3.737 4.568-.392.106-.803.162-1.227.162-.3 0-.593-.028-.877-.082.593 1.85 2.313 3.198 4.352 3.234-1.595 1.25-3.604 1.995-5.786 1.995-.376 0-.747-.022-1.112-.065 2.062 1.323 4.51 2.093 7.14 2.093 8.57 0 13.255-7.098 13.255-13.254 0-.2-.005-.402-.014-.602.91-.658 1.7-1.477 2.323-2.41z" />
                </svg>
                <div className="flex flex-col justify-start text-left">
                  <span className="font-medium">FLUX Anime</span>
                  <span className="text-xs text-gray-400">Japanese anime and manga style</span>
                </div>
              </div>
            </SelectItem>
            <SelectItem value="flux.anime" className="hover:bg-gray-800 data-[state=checked]:justify-start">
              <div className="flex items-center w-full py-2">
                <svg className="h-6 w-6 mr-3 flex-shrink-0" viewBox="0 0 24 24" fill="#1DA1F2">
                  <path d="M23.643 4.937c-.835.37-1.732.62-2.675.733.962-.576 1.7-1.49 2.048-2.578-.9.534-1.897.922-2.958 1.13-.85-.904-2.06-1.47-3.4-1.47-2.572 0-4.658 2.086-4.658 4.66 0 .364.042.718.12 1.06-3.873-.195-7.304-2.05-9.602-4.868-.4.69-.63 1.49-.63 2.342 0 1.616.823 3.043 2.072 3.878-.764-.025-1.482-.234-2.11-.583v.06c0 2.257 1.605 4.14 3.737 4.568-.392.106-.803.162-1.227.162-.3 0-.593-.028-.877-.082.593 1.85 2.313 3.198 4.352 3.234-1.595 1.25-3.604 1.995-5.786 1.995-.376 0-.747-.022-1.112-.065 2.062 1.323 4.51 2.093 7.14 2.093 8.57 0 13.255-7.098 13.255-13.254 0-.2-.005-.402-.014-.602.91-.658 1.7-1.477 2.323-2.41z" />
                </svg>
                <div className="flex flex-col justify-start text-left">
                  <span className="font-medium">FLUX Anime</span>
                  <span className="text-xs text-gray-400">Japanese anime and manga style</span>
                </div>
              </div>
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};

export default ModelSelection; 