"use client"

import { useState, useEffect } from "react"
import { Heart, MessageSquare, Share, Fullscreen, Download } from "lucide-react"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { toast } from "sonner"
import { ImageHistory as ImageHistoryType } from "@/lib/imageHistory"

interface ImageAuthor {
  id: string
  username: string
  userHandle: string
  avatar: string
  prompt: string
  images: string[]
  likes: number
  comments: number
  timestamp: string
  // 添加用于布局的属性
  span?: 1 | 2  // 卡片跨度
  highlight?: boolean // 是否突出显示
}

interface ImageHistoryProps {
  userId?: string
  initialImages?: ImageHistoryType[]
}

export default function ImageHistory({ userId, initialImages }: ImageHistoryProps) {
  // 如果有初始数据，直接使用格式化函数处理
  const initialAuthors = initialImages ? 
    initialImages.map((image, index) => {
      // 根据索引确定布局特性，而不是随机值
      const span = index % 7 === 3 ? 2 : 1
      const highlight = index % 5 === 0
      // 固定的点赞和评论数，避免随机值
      const likes = 10 + (index % 40)
      const comments = 2 + (index % 8)
      
      return {
        id: image.id,
        username: "可灵AI",
        userHandle: "@keling_ai",
        avatar: "/placeholder.svg?height=40&width=40",
        prompt: image.prompt,
        images: [image.image_url],
        likes,
        comments,
        timestamp: new Date(image.created_at).toLocaleString('zh-CN', {
          hour: 'numeric',
          minute: 'numeric',
          hour12: false
        }),
        span: span as 1 | 2,
        highlight
      }
    }) : [];
  
  const [authors, setAuthors] = useState<ImageAuthor[]>(initialAuthors)
  const [isLoading, setIsLoading] = useState(!initialImages || initialImages.length === 0)
  const [viewMode, setViewMode] = useState<'grid' | 'masonry' | 'cards'>('masonry')

  const formatImagesData = (images: ImageHistoryType[]) => {
    return images.map((image, index) => {
      // 根据索引确定布局特性，而不是随机值
      const span = index % 7 === 3 ? 2 : 1
      const highlight = index % 5 === 0
      // 固定的点赞和评论数，避免随机值
      const likes = 10 + (index % 40)
      const comments = 2 + (index % 8)
      
      return {
        id: image.id,
        username: "可灵AI",
        userHandle: "@keling_ai",
        avatar: "/placeholder.svg?height=40&width=40",
        prompt: image.prompt,
        images: [image.image_url], // 只显示一张图片
        likes,
        comments,
        timestamp: new Date(image.created_at).toLocaleString('zh-CN', {
          hour: 'numeric',
          minute: 'numeric',
          hour12: false
        }),
        span: span as 1 | 2,
        highlight
      }
    })
  }

  // 使用useEffect确保视图模式在客户端渲染时设置
  useEffect(() => {
    // 视图模式默认为cards，避免服务器/客户端不匹配
    setViewMode('cards')
  }, [])

  const fetchImages = async () => {
    // 如果有初始数据并且不是加载状态，不需要再次获取
    if (initialImages && initialImages.length > 0 && !isLoading) {
      return
    }
    
    try {
      setIsLoading(true)
      // 如果有userId，使用history API获取用户图片
      // 如果没有userId，使用public API获取公共图片
      const apiUrl = userId ? `/api/pic/history?userId=${userId}` : "/api/pic/public"
      const response = await fetch(apiUrl)
      if (!response.ok) {
        throw new Error("获取图片记录失败")
      }
      const data = await response.json()
      
      setAuthors(formatImagesData(data))
    } catch (error) {
      console.error("获取图片记录失败:", error)
      toast.error("获取图片记录失败")
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    // 如果没有初始数据，才需要从API获取
    if (!initialImages || initialImages.length === 0) {
      fetchImages()
    }
    // 绑定刷新方法到window对象
    window.refreshImageHistory = fetchImages
  }, [initialImages, userId])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-500"></div>
      </div>
    )
  }

  if (authors.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-gray-400">
        <p>暂无生成的图片</p>
      </div>
    )
  }

  // 视图切换按钮
  const ViewSwitcher = () => (
    <div className="flex space-x-2 mb-4">
      {/* <Button 
        variant={viewMode === 'grid' ? "default" : "outline"}
        size="sm"
        onClick={() => setViewMode('grid')}
        className="text-xs"
      >
        网格视图
      </Button>
      <Button 
        variant={viewMode === 'masonry' ? "default" : "outline"}
        size="sm"
        onClick={() => setViewMode('masonry')}
        className="text-xs"
      >
        瀑布流
      </Button>
      <Button 
        variant={viewMode === 'cards' ? "default" : "outline"}
        size="sm"
        onClick={() => setViewMode('cards')}
        className="text-xs"
      >
        卡片视图
      </Button> */}
    </div>
  )

  // 标准卡片视图
  // if (viewMode === 'cards') {
  //   return (
  //     <>
  //       <ViewSwitcher />
  //       <div className="space-y-6">
  //         {authors.map((author) => (
  //           <Card
  //             key={author.id}
  //             className={`overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-shadow ${
  //               author.highlight ? 'border-2 border-emerald-500' : 'bg-gray-900 border-gray-800'
  //             }`}
  //           >
  //             {/* Author Header */}
  //             <div className="p-4 flex items-center justify-between border-b border-gray-800 bg-gray-900">
  //               <div className="flex items-center">
  //                 <div className="w-10 h-10 rounded-full bg-gray-700 overflow-hidden mr-3 border-2 border-emerald-500/30">
  //                   <img
  //                     src={author.avatar || "/placeholder.svg"}
  //                     alt={author.username}
  //                     className="w-full h-full object-cover"
  //                   />
  //                 </div>
  //                 <div>
  //                   <div className="font-semibold text-white">{author.username}</div>
  //                   <div className="text-sm text-emerald-300">{author.userHandle}</div>
  //                 </div>
  //               </div>
  //               <div className="text-xs text-gray-400 bg-gray-800 px-2 py-1 rounded-full">{author.timestamp}</div>
  //             </div>

  //             {/* Prompt */}
  //             <div className="px-4 py-3 border-b border-gray-800 bg-gray-900/70">
  //               <p className="text-sm text-gray-300">{author.prompt}</p>
  //             </div>

  //             {/* Images Grid */}
  //             <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 p-2 bg-gray-950">
  //               {author.images.map((imageUrl, index) => (
  //                 <div key={index} className="relative aspect-[3/4] group overflow-hidden rounded-lg">
  //                   <img
  //                     src={imageUrl || "/placeholder.svg"}
  //                     alt={`Generated by ${author.username}`}
  //                     className="w-full h-full object-cover rounded-lg"
  //                   />
  //                   <div className="absolute bottom-2 right-2 text-xs bg-black bg-opacity-70 px-2 py-1 rounded-full text-emerald-400 font-medium">
  //                     可灵AI
  //                   </div>
  //                 </div>
  //               ))}
  //             </div>

  //             {/* Actions */}
  //             <div className="p-4 flex items-center justify-between border-t border-gray-800 bg-gray-900/80">
  //               <div className="flex items-center space-x-6">
  //                 <button className="flex items-center text-gray-400 hover:text-rose-500 transition-colors">
  //                   <Heart className="h-5 w-5 mr-1" />
  //                   <span className="text-sm">{author.likes}</span>
  //                 </button>
  //                 <button className="flex items-center text-gray-400 hover:text-cyan-500 transition-colors">
  //                   <MessageSquare className="h-5 w-5 mr-1" />
  //                   <span className="text-sm">{author.comments}</span>
  //                 </button>
  //                 <button className="flex items-center text-gray-400 hover:text-emerald-500 transition-colors">
  //                   <Share className="h-5 w-5" />
  //                 </button>
  //               </div>
  //               <Button
  //                 variant="outline"
  //                 size="sm"
  //                 className="text-xs bg-gray-800 border-gray-700 text-emerald-300 hover:bg-gray-700 hover:text-emerald-200"
  //               >
  //                 查看更多
  //               </Button>
  //             </div>
  //           </Card>
  //         ))}
  //       </div>
  //     </>
  //   )
  // }

  // // 网格视图
  // if (viewMode === 'grid') {
  //   return (
  //     <>
  //       <ViewSwitcher />
  //       <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
  //         {authors.map((author) => (
  //           <div 
  //             key={author.id} 
  //             className={`relative group overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 ${
  //               author.highlight ? 'ring-2 ring-emerald-500 ring-offset-2 ring-offset-background' : ''
  //             } ${author.span === 2 ? 'sm:col-span-2' : ''}`}
  //           >
  //             <div className="aspect-[3/4] bg-gray-900 overflow-hidden">
  //               <img
  //                 src={author.images[0] || "/placeholder.svg"}
  //                 alt={author.prompt}
  //                 className="w-full h-full object-cover transition-transform group-hover:scale-105"
  //               />
  //             </div>
              
  //             {/* 悬停时显示的信息 */}
  //             <div className="absolute inset-0 bg-gradient-to-t from-black via-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-4">
  //               <p className="text-sm text-gray-200 line-clamp-2 mb-2">{author.prompt}</p>
  //               <div className="flex justify-between items-center">
  //                 <div className="flex space-x-3">
  //                   <button className="text-white/80 hover:text-white">
  //                     <Heart size={18} />
  //                   </button>
  //                   <button className="text-white/80 hover:text-white">
  //                     <Download size={18} />
  //                   </button>
  //                   <button className="text-white/80 hover:text-white">
  //                     <Fullscreen size={18} />
  //                   </button>
  //                 </div>
  //                 <div className="text-xs text-gray-400">{author.timestamp}</div>
  //               </div>
  //             </div>
              
  //             {/* 永久可见的标签 */}
  //             <div className="absolute top-2 right-2 text-xs bg-black bg-opacity-70 px-2 py-1 rounded-full text-emerald-400 font-medium">
  //               可灵AI
  //             </div>
  //           </div>
  //         ))}
  //       </div>
  //     </>
  //   )
  // }

  // 瀑布流视图（默认）
  return (
    <>
      <ViewSwitcher />
      <div className="columns-1 sm:columns-2 md:columns-3 lg:columns-4 gap-4 space-y-4">
        {authors.map((author, index) => (
          <div 
            key={author.id} 
            className={`break-inside-avoid mb-4 relative group overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 ${
              author.highlight ? 'ring-2 ring-emerald-500' : ''
            }`}
          >
            {/* 图片容器 - 使用索引来确定高度，而不是随机值 */}
            <div className={`relative overflow-hidden ${
              index % 2 === 0 ? 'aspect-[3/4]' : 'aspect-square'
            }`}>
              <img
                src={author.images[0] || "/placeholder.svg"}
                alt={author.prompt}
                className="w-full h-full object-cover transition-transform group-hover:scale-105"
              />
              
              {/* 图片上的渐变遮罩 */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              
              {/* 顶部标签 */}
              <div className="absolute top-2 right-2 text-xs bg-black bg-opacity-70 px-2 py-1 rounded-full text-emerald-400 font-medium">
                可灵AI
              </div>
            </div>
            
            {/* 内容区域 */}
            <div className="p-3 bg-gray-900">
              <p className="text-sm text-gray-300 line-clamp-2 mb-2">{author.prompt}</p>
              <div className="flex justify-between items-center">
                <div className="flex space-x-4">
                  <button className="flex items-center text-gray-400 hover:text-rose-500 transition-colors">
                    <Heart className="h-4 w-4 mr-1" />
                    <span className="text-xs">{author.likes}</span>
                  </button>
                  <button className="flex items-center text-gray-400 hover:text-cyan-500 transition-colors">
                    <MessageSquare className="h-4 w-4 mr-1" />
                    <span className="text-xs">{author.comments}</span>
                  </button>
                </div>
                <div className="text-xs text-gray-500">{author.timestamp}</div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </>
  )
}

