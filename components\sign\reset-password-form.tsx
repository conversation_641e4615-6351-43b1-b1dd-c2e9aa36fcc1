"use client";

import {
  <PERSON>,
  <PERSON>Content,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";
import { FormEvent, useState } from "react";
import { toast } from "sonner";
import { resetPassword } from "@/lib/supabase";

export default function ResetPasswordForm({
  className,
  ...props
}: React.ComponentPropsWithoutRef<"div">) {
  const t = useTranslations();
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [sent, setSent] = useState(false);

  const handleResetPassword = async (e: FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      toast.error(t("sign_modal.email_required"));
      return;
    }
    
    setLoading(true);
    
    try {
      const { error } = await resetPassword(email);
      
      if (error) {
        toast.error(error.message);
        return;
      }
      
      setSent(true);
      toast.success(t("sign_modal.reset_password_sent"));
    } catch (error) {
      toast.error(t("sign_modal.reset_password_failed"));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card>
        <CardHeader className="text-center">
          <CardTitle className="text-xl">
            {t("sign_modal.reset_password_title")}
          </CardTitle>
          <CardDescription>
            {t("sign_modal.reset_password_description")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {!sent ? (
            <form className="grid gap-6" onSubmit={handleResetPassword}>
              <div className="grid gap-2">
                <Label htmlFor="email">{t("sign_modal.email")}</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </div>
              <Button type="submit" className="w-full" disabled={loading}>
                {loading 
                  ? t("sign_modal.sending") 
                  : t("sign_modal.send_reset_link")}
              </Button>
              <div className="text-center text-sm">
                <Button 
                  variant="link" 
                  className="p-0 h-auto" 
                  onClick={() => window.location.href = "/loginseo"}
                >
                  {t("sign_modal.back_to_login")}
                </Button>
              </div>
            </form>
          ) : (
            <div className="grid gap-6">
              <p className="text-center">
                {t("sign_modal.reset_password_check_email")}
              </p>
              <Button 
                className="w-full" 
                onClick={() => window.location.href = "/loginseo"}
              >
                {t("sign_modal.back_to_login")}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 