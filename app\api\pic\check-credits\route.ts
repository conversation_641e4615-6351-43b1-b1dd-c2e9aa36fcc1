import { NextResponse } from "next/server";
import { getUserCredits, CreditsAmount } from "@/services/credit";
export const runtime = "edge";

export async function POST(req: Request) {
  try {
    const { guestId } = await req.json();

    if (!guestId) {
      return NextResponse.json(
        { error: "缺少用户ID" },
        { status: 400 }
      );
    }

    // 检查用户积分是否足够
    const userCredits = await getUserCredits(guestId);
    const hasEnoughCredits = userCredits.left_credits >= CreditsAmount.ImageGenerationCost;

    return NextResponse.json({
      hasEnoughCredits,
      credits: userCredits.left_credits,
      requiredCredits: CreditsAmount.ImageGenerationCost
    });

  } catch (error) {
    console.error('积分检查错误:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "检查积分失败" },
      { status: 500 }
    );
  }
} 