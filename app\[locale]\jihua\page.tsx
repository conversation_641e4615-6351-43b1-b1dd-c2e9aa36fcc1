// Dashboard share page - Server Component
import { Metadata } from "next"
import { getTranslations } from "next-intl/server"
// import SharesDisplay from "@/components/art/SharesDisplay"
import DashboardClient from "@/components/dash/DashboardClient"
// import { fetchSharesData } from "@/lib/shares"
import ImageGallery from "@/components/keling/ImageGallery"
import PngToText from "@/components/keling/PngToText"
import ImageHistory from "@/components/keling/imagehistory"
import { getImagesByUser } from "@/lib/imageHistory"
import { auth } from "@/auth"
import Zidong from "@/components/keling/zidong"
import Results from "@/components/modou/Results"
import { ImageHistory as ImageHistoryType } from "@/lib/imageHistory"
import AutoGenerate from "@/components/keling/AutoGenerate"
import PromptLibrary from "@/components/keling/PromptLibrary"

export const runtime = "edge";

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("pages.landing")
  return {
    title: t("title"),
    description: t("description"),
  }
}

// 为未登录用户指定一个默认用户ID（您需要使用一个真实存在的用户ID）
const DEFAULT_USER_ID = "47618b3f-6be6-473f-93e3-8d624efdc88c"; // 替换为真实的用户ID

export default async function SharePage() {
  // 在服务器端获取auth session
  const session = await auth()
  const userId = session?.user?.id || session?.user?.uuid || ""
  const isLoggedIn = !!userId

  // 在服务器端获取图片数据，而不是客户端获取，有利于SEO
  // 已登录用户获取自己的数据，未登录用户获取默认用户的数据
  const actualUserId = isLoggedIn ? userId : DEFAULT_USER_ID
  
  // 获取图片历史数据
  let images: ImageHistoryType[] = []
  try {
    images = await getImagesByUser(actualUserId, 30)
  } catch (error) {
    console.error("获取图片历史失败:", error)
  }

  return (
    <DashboardClient>
      <div className="flex-1 overflow-y-auto p-4 md:p-6">
        {/* 页面标题 */}
        {/* <div className="mb-6">
          <h1 className="text-2xl font-bold mb-2">共享中心</h1>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            浏览社区分享和您的创建内容
          </p>
        </div> */}
        {/* <Zidong /> */}
        {/* <AutoGenerate /> */}
        {/* <ImageGallery />  */}
 
        {/* <PngToText /> */}
        
        {/* 条件渲染: 未登录用户看到 ImageHistory (使用默认用户ID), 已登录用户看到 Results */}
        {/* {!isLoggedIn ? (
          <ImageHistory initialImages={images} />
        ) : (
          <Results userId={userId} limit={30} />
        )} */}
        
        {/* 服务器渲染内容：将数据传递给客户端展示组件 */}
        <div className="grid grid-cols-1 gap-6">
          {/* <SharesDisplay 
            generatedTweets={generatedTweets}
            sampleTweets={sampleTweets}
            className="border-t-0 pt-0 mt-0"
          /> */}
        </div>
        <PromptLibrary />
      </div>
    </DashboardClient>
  )
} 