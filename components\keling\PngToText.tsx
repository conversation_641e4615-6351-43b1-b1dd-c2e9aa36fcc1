"use client"

import { useState } from "react"
import { Loader2, Upload, <PERSON>rkles, X, Copy, Check, Languages, Palette, Image as ImageIcon, Link } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { toast } from "sonner"
import Image from "next/image"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { Input } from "@/components/ui/input"

type PromptType = "general" | "portrait" | "landscape" | "object" | "artistic"
type Language = "en" | "zh" | "ja" | "ko"
type Style = "realistic" | "anime" | "cartoon" | "watercolor" | "oil-painting" | "pixel-art"

interface Example {
  image: string
  prompt: string
  type: string
}

const examples: Example[] = [
  {
    image: "/examples/portrait.jpg",
    prompt: "A beautiful woman with long flowing hair, wearing a white dress, standing in a field of flowers, soft lighting, cinematic, 8k",
    type: "Portrait"
  },
  {
    image: "/examples/landscape.jpg",
    prompt: "A serene mountain landscape at sunset, golden light reflecting on a calm lake, misty atmosphere, dramatic clouds, 8k",
    type: "Landscape"
  },
  {
    image: "/examples/object.jpg",
    prompt: "A vintage camera on a wooden table, soft natural lighting, shallow depth of field, detailed textures, 8k",
    type: "Object"
  }
]

const languages = [
  { value: "en", label: "English" },
  { value: "zh", label: "中文" },
  { value: "ja", label: "日本語" },
  { value: "ko", label: "한국어" }
]

const styles = [
  { value: "realistic", label: "Realistic" },
  { value: "anime", label: "Anime" },
  { value: "cartoon", label: "Cartoon" },
  { value: "watercolor", label: "Watercolor" },
  { value: "oil-painting", label: "Oil Painting" },
  { value: "pixel-art", label: "Pixel Art" }
]

const promptTypes = [
  {
    value: "general",
    label: "General",
    icon: ImageIcon
  },
  {
    value: "midjourney",
    label: "Midjourney",
    icon: ImageIcon
  },
  {
    value: "dalle",
    label: "DALL-E",
    icon: ImageIcon
  },
  {
    value: "stable-diffusion",
    label: "Stable Diffusion",
    icon: ImageIcon
  },
  {
    value: "flux",
    label: "Flux",
    icon: ImageIcon
  }
]

export default function PngToText() {
  const [selectedImage, setSelectedImage] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [imageUrl, setImageUrl] = useState("")
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedPrompts, setGeneratedPrompts] = useState<string[]>([])
  const [promptType, setPromptType] = useState<PromptType>("general")
  const [language, setLanguage] = useState<Language>("en")
  const [style, setStyle] = useState<Style>("realistic")
  const [wordCount, setWordCount] = useState(50)
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null)
  const [inputMethod, setInputMethod] = useState<"upload" | "url">("upload")

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setSelectedImage(file)
      setGeneratedPrompts([])
      
      // Create preview URL
      const reader = new FileReader()
      reader.onloadend = () => {
        setImagePreview(reader.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleUrlChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const url = e.target.value
    setImageUrl(url)
    
    if (url) {
      try {
        // Validate URL
        new URL(url)
        
        // Check if URL is an image
        const response = await fetch(url)
        const contentType = response.headers.get("content-type")
        if (contentType?.startsWith("image/")) {
          setImagePreview(url)
          setSelectedImage(null)
          setGeneratedPrompts([])
        }
      } catch (error) {
        // URL is invalid or not an image, do nothing
      }
    }
  }

  const handleRemoveImage = () => {
    setSelectedImage(null)
    setImagePreview(null)
    setImageUrl("")
    setGeneratedPrompts([])
  }

  const handleCopyPrompt = async (prompt: string, index: number) => {
    try {
      await navigator.clipboard.writeText(prompt)
      setCopiedIndex(index)
      toast.success("Prompt copied")
      setTimeout(() => setCopiedIndex(null), 2000)
    } catch (err) {
      toast.error("Copy failed")
    }
  }

  const handleGenerate = async () => {
    if (!imagePreview) {
      toast.error("Please upload an image first")
      return
    }

    try {
      setIsGenerating(true)
      const formData = new FormData()
      
      // If we have a file, use it, otherwise use the URL
      if (selectedImage) {
        formData.append("image", selectedImage)
      } else if (imageUrl) {
        // Fetch the image from URL and convert to File
        const response = await fetch(imageUrl)
        const blob = await response.blob()
        const file = new File([blob], "image.jpg", { type: blob.type })
        formData.append("image", file)
      }
      
      formData.append("promptType", promptType)
      formData.append("language", language)
      formData.append("wordCount", wordCount.toString())

      const response = await fetch("/api/pngtotext", {
        method: "POST",
        body: formData,
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || "Failed to generate prompts")
      }

      const data = await response.json()
      const prompts = data.content.split("\n").filter((p: string) => p.trim())
      setGeneratedPrompts(prompts)
      toast.success("Prompts generated successfully")
    } catch (error) {
      console.error("Generation failed:", error)
      toast.error(error instanceof Error ? error.message : "Failed to generate prompts")
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <div className="flex flex-col min-h-screen bg-gray-950 text-gray-200">
      {/* Header Section */}
      <div className="w-full bg-gray-950 border-b border-gray-800">
        <div className="container mx-auto px-6 py-12 text-center">
          <h1 className="text-3xl md:text-4xl font-bold text-emerald-400 mb-4">Free Image to Prompt Generator</h1>
          <p className="text-lg text-gray-300 max-w-2xl mx-auto">
            Discover the power of image to prompt generator, learn how to use AI to create text prompts from images, and explore the best free tools available.
          </p>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8 max-w-7xl">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Input Section */}
          <div className="space-y-6">
            {/* Image Input Section */}
            <Card className="bg-gray-900 border-gray-800 p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <Upload className="h-5 w-5 mr-2 text-emerald-400" />
                  <span className="text-emerald-100 font-medium">Image Input</span>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant={inputMethod === "upload" ? "default" : "outline"}
                    size="sm"
                    className="w-24"
                    onClick={() => setInputMethod("upload")}
                  >
                    Upload
                  </Button>
                  <Button
                    variant={inputMethod === "url" ? "default" : "outline"}
                    size="sm"
                    className="w-24"
                    onClick={() => setInputMethod("url")}
                  >
                    URL
                  </Button>
                </div>
              </div>

              {inputMethod === "upload" ? (
                !imagePreview ? (
                  <div className="border-2 border-dashed border-gray-700 rounded-lg p-8 flex flex-col items-center justify-center h-48 hover:border-emerald-500 transition-colors">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                      id="image-upload"
                    />
                    <label htmlFor="image-upload" className="cursor-pointer flex flex-col items-center">
                      <Upload className="h-12 w-12 mb-4 text-emerald-400" />
                      <div className="text-lg text-emerald-400 mb-2">Click to Upload</div>
                      <div className="text-sm text-gray-400">or drag and drop</div>
                    </label>
                  </div>
                ) : (
                  <div className="relative h-48 w-full">
                    <Image
                      src={imagePreview}
                      alt="Preview"
                      fill
                      className="object-contain rounded-lg"
                    />
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute top-2 right-2 bg-gray-900/80 hover:bg-gray-800/80"
                      onClick={handleRemoveImage}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                )
              ) : (
                <div className="space-y-4">
                  <div className="border-2 border-dashed border-gray-700 rounded-lg p-8 flex flex-col items-center justify-center h-48 hover:border-emerald-500 transition-colors">
                    <div className="flex flex-col items-center w-full max-w-md">
                      <Link className="h-12 w-12 mb-4 text-emerald-400" />
                      <div className="text-lg text-emerald-400 mb-2">Enter Image URL</div>
                      <div className="flex space-x-2 w-full">
                        <Input
                          type="url"
                          placeholder="https://example.com/image.jpg"
                          value={imageUrl}
                          onChange={handleUrlChange}
                          className="flex-1"
                        />
                      </div>
                      <div className="text-sm text-gray-400 mt-2">Supports jpg, png, gif formats</div>
                    </div>
                  </div>
                  {imagePreview && (
                    <div className="relative h-48 w-full">
                      <Image
                        src={imagePreview}
                        alt="Preview"
                        fill
                        className="object-contain rounded-lg"
                      />
                      <Button
                        variant="ghost"
                        size="icon"
                        className="absolute top-2 right-2 bg-gray-900/80 hover:bg-gray-800/80"
                        onClick={handleRemoveImage}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </Card>

            {/* Settings Section */}
            <Card className="bg-gray-900 border-gray-800 p-6 space-y-6">
              {/* Language Selection */}
              <div className="space-y-2">
                <div className="flex items-center">
                  <Languages className="h-5 w-5 mr-2 text-emerald-400" />
                  <span className="text-emerald-100 font-medium">Language</span>
                </div>
                <Select value={language} onValueChange={(value: Language) => setLanguage(value)}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select language" />
                  </SelectTrigger>
                  <SelectContent>
                    {languages.map((lang) => (
                      <SelectItem key={lang.value} value={lang.value}>
                        {lang.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Prompt Type Selection */}
              <div className="space-y-2">
                <div className="flex items-center">
                  <ImageIcon className="h-5 w-5 mr-2 text-emerald-400" />
                  <span className="text-emerald-100 font-medium">Prompt Type</span>
                </div>
                <div className="flex flex-wrap gap-2">
                  {promptTypes.map((type) => (
                    <Button
                      key={type.value}
                      variant={promptType === type.value ? "default" : "outline"}
                      className={`${
                        promptType === type.value
                          ? "bg-emerald-500 hover:bg-emerald-600"
                          : "border-gray-700 hover:border-emerald-500"
                      }`}
                      onClick={() => setPromptType(type.value as PromptType)}
                    >
                      <type.icon className="h-4 w-4 mr-2" />
                      {type.label}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Word Count Slider */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-emerald-100 font-medium">Prompt Length</span>
                  <span className="text-sm text-gray-400">{wordCount} words</span>
                </div>
                <Slider
                  value={[wordCount]}
                  onValueChange={(value) => setWordCount(value[0])}
                  min={20}
                  max={100}
                  step={10}
                  className="w-full"
                />
              </div>
            </Card>

            {/* Generate Button */}
            <Button
              className="w-full h-12 bg-gradient-to-r from-emerald-500 to-cyan-500 hover:from-emerald-600 hover:to-cyan-600 text-white font-medium shadow-lg"
              onClick={handleGenerate}
              disabled={isGenerating || !imagePreview}
            >
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  Generate Prompts
                  <Sparkles className="ml-2 h-4 w-4" />
                </>
              )}
            </Button>
          </div>

          {/* Right Results Section */}
          <div className="space-y-6">
            {generatedPrompts.length > 0 ? (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-emerald-400">Generated Prompts:</h3>
                {generatedPrompts.map((prompt, index) => (
                  <Card key={index} className="p-4 bg-gray-900 border-gray-800 relative">
                    <div className="text-sm text-gray-300 whitespace-pre-wrap pr-12">{prompt}</div>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute top-2 right-2"
                      onClick={() => handleCopyPrompt(prompt, index)}
                    >
                      {copiedIndex === index ? (
                        <Check className="h-4 w-4 text-emerald-400" />
                      ) : (
                        <Copy className="h-4 w-4 text-gray-400 hover:text-emerald-400" />
                      )}
                    </Button>
                  </Card>
                ))}
              </div>
            ) : (
              <Card className="p-6 bg-gray-900 border-gray-800 h-full flex items-center justify-center">
                <div className="text-center">
                  <Sparkles className="h-12 w-12 mx-auto mb-4 text-emerald-400" />
                  <p className="text-lg text-gray-300">Upload an image and click generate</p>
                  <p className="text-sm text-gray-400 mt-2">We'll generate 3 high-quality prompts for you</p>
                </div>
              </Card>
            )}
          </div>
        </div>

        {/* Examples Section */}
        <div className="mt-16">
          <h2 className="text-2xl font-bold text-emerald-400 mb-6 text-center">Examples</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {examples.map((example, index) => (
              <Card key={index} className="bg-gray-900 border-gray-800 overflow-hidden group">
                <div className="relative aspect-square">
                  <Image
                    src={example.image}
                    alt={example.prompt}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                </div>
                <div className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs text-emerald-400 bg-emerald-900/50 px-2 py-1 rounded">
                      {example.type}
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-xs text-gray-400 hover:text-emerald-400"
                      onClick={() => handleCopyPrompt(example.prompt, -1)}
                    >
                      <Copy className="h-3 w-3 mr-1" />
                      Copy Prompt
                    </Button>
                  </div>
                  <p className="text-sm text-gray-300 line-clamp-2">{example.prompt}</p>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
} 