@baseUrl = http://localhost:3000
@apiKey = ****************************************

### Test Replicate API Token
GET https://api.replicate.com/v1/account
Authorization: Bearer {{apiKey}}

### Test Image Generation
POST {{baseUrl}}/api/replicate
Content-Type: application/json

{
  "prompt": "A beautiful girl running with 2 cats in a sunny park, photorealistic style",
  "categoryId": "test-category",
  "prompt_id": "test-{{$timestamp}}",
  "width": 768,
  "height": 1152,
  "count": 1,
  "aspect_ratio": "3:4",
  "style": "photorealistic"
}

### Test Image Generation with Different Style
POST {{baseUrl}}/api/replicate
Content-Type: application/json

{
  "prompt": "A magical forest with glowing mushrooms and fairy lights, fantasy style",
  "categoryId": "test-category",
  "prompt_id": "test-{{$timestamp}}",
  "width": 768,
  "height": 768,
  "count": 1,
  "aspect_ratio": "1:1",
  "style": "fantasy"
}

### Test Error Handling - Missing Required Fields
POST {{baseUrl}}/api/replicate
Content-Type: application/json

{
  "prompt": "Test error handling"
}

### Test Error Handling - Invalid Dimensions
POST {{baseUrl}}/api/replicate
Content-Type: application/json

{
  "prompt": "Test invalid dimensions",
  "categoryId": "test-category",
  "prompt_id": "test-{{$timestamp}}",
  "width": 100,
  "height": 100,
  "count": 1,
  "aspect_ratio": "1:1"
} 