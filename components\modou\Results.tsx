"use client"
import { useEffect, useState } from 'react';
import { toast } from "sonner";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import Image from 'next/image';
import { Loader2 } from "lucide-react";
import { Heart, Download } from "lucide-react";

// Define the type for the data returned from the API
interface ImagesingItem {
  id: string;
  user_id: string;
  created_at: string;
  image_url: string;
  prompt?: string;
  title?: string;
  // Add other fields from your imagesing table as needed
}

// Define a type for generation history items
interface GenerationHistoryItem {
  id: string;
  prompt: string;
  images: string[];
  timestamp: Date;
  isLoading?: boolean;
  progress?: number;
  showInPlace?: boolean;
}

interface ResultsProps {
  userId?: string;
  limit?: number;
  isAuthenticated?: boolean;
  generationHistory?: GenerationHistoryItem[];
  isMobile?: boolean;
  loading?: boolean;
  debug?: boolean;
  disableAutoFetch?: boolean;
}

export default function Results({ 
  userId, 
  limit = 10, 
  isAuthenticated,
  generationHistory = [],
  isMobile = false,
  loading = false,
  debug = false,
  disableAutoFetch = false
}: ResultsProps) {
  const [data, setData] = useState<ImagesingItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(!disableAutoFetch);
  const [error, setError] = useState<string | null>(null);

  // Function to parse image URL which might be a JSON string array
  const parseImageUrl = (imageUrl: string): string => {
    try {
      const parsed = JSON.parse(imageUrl);
      // If it's an array, return the first item
      if (Array.isArray(parsed) && parsed.length > 0) {
        return parsed[0];
      }
      // If it's a string after parsing (unlikely but possible), return it
      if (typeof parsed === 'string') {
        return parsed;
      }
      // Fallback
      return '';
    } catch (e) {
      // If it's not valid JSON, return the original string
      return imageUrl;
    }
  };

  useEffect(() => {
    // Skip fetching if disableAutoFetch is true
    if (disableAutoFetch) {
      setIsLoading(false);
      return;
    }
    
    const fetchImages = async () => {
      try {
        setIsLoading(true);
        const targetUserId = userId || '47618b3f-6be6-473f-93e3-8d624efdc88c';
        
        const response = await fetch(`/api/pic/imagesing?userId=${targetUserId}&limit=${limit}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache'
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const result = await response.json();
        setData(result);
      } catch (err) {
        console.error('Failed to fetch images:', err);
        setError('Failed to load images. Please try again later.');
        toast.error("Failed to load images. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchImages();
  }, [userId, limit, disableAutoFetch]);

  // If loading and no generation history provided
  if (isLoading && generationHistory.length === 0) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {Array(3).fill(0).map((_, index) => (
          <Card key={index} className="overflow-hidden">
            <CardHeader>
              <Skeleton className="h-4 w-2/3" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-48 w-full" />
            </CardContent>
            <CardFooter>
              <Skeleton className="h-4 w-full" />
            </CardFooter>
          </Card>
        ))}
      </div>
    );
  }

  // If there's an error and no generation history
  if (error && generationHistory.length === 0 && !disableAutoFetch) {
    return (
      <div className="flex flex-col items-center justify-center p-8">
        <p className="text-red-500 mb-4">{error}</p>
        <Button 
          onClick={() => window.location.reload()} 
          variant="outline"
        >
          Try Again
        </Button>
      </div>
    );
  }

  // If there's no history to display
  if (data.length === 0 && generationHistory.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-8">
        <p className="text-gray-500 mb-4">No image history found</p>
      </div>
    );
  }

  // If showing generation history
  if (generationHistory.length > 0) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {generationHistory.map((item) => (
          <div 
            key={item.id} 
            className="relative group overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <div className="aspect-[3/4] bg-gray-900 overflow-hidden">
              {item.isLoading ? (
                <div className="w-full h-full flex flex-col items-center justify-center relative bg-gray-800">
                  <Loader2 className="h-10 w-10 text-emerald-400 animate-spin mb-2" />
                  <p className="text-gray-400 text-sm animate-pulse">
                    Generating {item.progress ? `${Math.round(item.progress * 100)}%` : '...'}
                  </p>
                </div>
              ) : item.images.length > 0 ? (
                <img
                  src={item.images[0]}
                  alt="Generated image"
                  className="w-full h-full object-cover transition-transform group-hover:scale-105"
                />
              ) : (
                <div className="flex items-center justify-center h-full bg-gray-800">
                  <p className="text-gray-400">No image available</p>
                </div>
              )}
            </div>
            
            {/* 悬停时显示的信息 */}
            {!item.isLoading && item.images.length > 0 && (
              <div className="absolute inset-0 bg-gradient-to-t from-black via-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-4">
                <p className="text-sm text-gray-200 line-clamp-2 mb-2">{item.prompt || "AI Generation"}</p>
                <div className="flex justify-between items-center">
                  <div className="flex space-x-3">
                    <button className="text-white/80 hover:text-white">
                      <Heart className="h-4 w-4" />
                    </button>
                    <button className="text-white/80 hover:text-white">
                      <Download className="h-4 w-4" />
                    </button>
                  </div>
                  <div className="text-xs text-gray-400">
                    {new Date(item.timestamp).toLocaleDateString()} {new Date(item.timestamp).toLocaleTimeString().slice(0, 5)}
                  </div>
                </div>
              </div>
            )}
            
            {/* 永久可见的标签 */}
            <div className="absolute top-2 right-2 text-xs bg-black bg-opacity-70 px-2 py-1 rounded-full text-emerald-400 font-medium">
              {item.isLoading ? '生成中' : (item.images.length > 1 ? `${item.images.length}张` : '已生成')}
            </div>
            
            {/* Multiple images indicator */}
            {!item.isLoading && item.images.length > 1 && (
              <div className="absolute bottom-2 right-2 bg-black/60 text-white text-xs px-2 py-1 rounded-full">
                +{item.images.length - 1}
              </div>
            )}
          </div>
        ))}

        {/* Show fetched data from API if not using generation history exclusively */}
        {!disableAutoFetch && data.map((item) => (
          <div 
            key={item.id} 
            className="relative group overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <div className="aspect-[3/4] bg-gray-900 overflow-hidden">
              {item.image_url ? (
                <img
                  src={parseImageUrl(item.image_url)}
                  alt={item.title || "Generated image"}
                  className="w-full h-full object-cover transition-transform group-hover:scale-105"
                />
              ) : (
                <div className="flex items-center justify-center h-full bg-gray-800 rounded-md">
                  <p className="text-gray-400">No image available</p>
                </div>
              )}
            </div>
            
            {/* 悬停时显示的信息 */}
            <div className="absolute inset-0 bg-gradient-to-t from-black via-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-4">
              <p className="text-sm text-gray-200 line-clamp-2 mb-2">{item.prompt || item.title || "Untitled Image"}</p>
              <div className="flex justify-between items-center">
                <div className="flex space-x-3">
                  <button className="text-white/80 hover:text-white">
                    <Heart className="h-4 w-4" />
                  </button>
                  <button className="text-white/80 hover:text-white">
                    <Download className="h-4 w-4" />
                  </button>
                </div>
                <div className="text-xs text-gray-400">{new Date(item.created_at).toLocaleDateString()}</div>
              </div>
            </div>
            
            {/* 永久可见的标签 */}
            <div className="absolute top-2 right-2 text-xs bg-black bg-opacity-70 px-2 py-1 rounded-full text-emerald-400 font-medium">
              历史
            </div>
          </div>
        ))}
      </div>
    );
  }

  // Default view - showing fetched API data
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {data.map((item) => (
        <div 
          key={item.id} 
          className="relative group overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <div className="aspect-[3/4] bg-gray-900 overflow-hidden">
            {item.image_url ? (
              <img
                src={parseImageUrl(item.image_url)}
                alt={item.title || "Generated image"}
                className="w-full h-full object-cover transition-transform group-hover:scale-105"
              />
            ) : (
              <div className="flex items-center justify-center h-full bg-gray-800 rounded-md">
                <p className="text-gray-400">No image available</p>
              </div>
            )}
          </div>
          
          {/* 悬停时显示的信息 */}
          <div className="absolute inset-0 bg-gradient-to-t from-black via-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-4">
            <p className="text-sm text-gray-200 line-clamp-2 mb-2">{item.prompt || item.title || "Untitled Image"}</p>
            <div className="flex justify-between items-center">
              <div className="flex space-x-3">
                <button className="text-white/80 hover:text-white">
                  <Heart className="h-4 w-4" />
                </button>
                <button className="text-white/80 hover:text-white">
                  <Download className="h-4 w-4" />
                </button>
              </div>
              <div className="text-xs text-gray-400">{new Date(item.created_at).toLocaleDateString()}</div>
            </div>
          </div>
          
          {/* 永久可见的标签 */}
          <div className="absolute top-2 right-2 text-xs bg-black bg-opacity-70 px-2 py-1 rounded-full text-emerald-400 font-medium">
            历史
          </div>
        </div>
      ))}
    </div>
  );
}
