import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import { getUserCredits } from '@/services/credit';
import { getUserUuid } from '@/services/user';
import { supabase } from '@/lib/supabase';
import { cookies, headers } from 'next/headers';
export const runtime = "edge";

export async function GET() {
  try {
    const session = await auth();
    let user_uuid = null;
    
    if (session?.user) {
      user_uuid = await getUserUuid();
      console.log('NextAuth认证用户ID:', user_uuid);
    }
    
    if (!user_uuid) {
      const cookieStore = cookies();
      const headersList = headers();
      const supabaseServer = supabase.auth.getUser();
      
      const { data, error } = await supabaseServer;
      
      if (data?.user && !error) {
        user_uuid = data.user.id;
        console.log('Supabase认证用户ID:', user_uuid);
      }
    }
    
    if (!user_uuid) {
      return NextResponse.json(
        { error: 'Unauthorized: No valid authentication found' }, 
        { status: 401 }
      );
    }
    
    const userCredits = await getUserCredits(user_uuid);
    console.log(`用户 ${user_uuid} 的积分:`, userCredits);
    
    return NextResponse.json({
      left_credits: userCredits.left_credits || 0,
      is_pro: userCredits.is_pro || false,
      is_recharged: userCredits.is_recharged || false,
    });
    
  } catch (error) {
    console.error('Error fetching user credits:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user credits' },
      { status: 500 }
    );
  }
} 