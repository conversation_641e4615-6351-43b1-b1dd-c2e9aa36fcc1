"use client";

import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";
import { FormEvent, useState } from "react";
import { toast } from "sonner";
import { signUpWithEmail } from "@/lib/supabase";
import { signIn } from "next-auth/react";
import { SiGoogle } from "react-icons/si";

export default function SignUpForm({
  className,
  onToggle,
  ...props
}: React.ComponentPropsWithoutRef<"div"> & {
  onToggle?: () => void;
}) {
  const t = useTranslations();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [loading, setLoading] = useState(false);

  const handleSignUp = async (e: FormEvent) => {
    e.preventDefault();
    
    if (password !== confirmPassword) {
      toast.error(t("sign_modal.passwords_not_match"));
      return;
    }
    
    setLoading(true);
    
    try {
      const { data, error } = await signUpWithEmail(email, password);
      
      if (error) {
        toast.error(error.message);
        return;
      }
      
      if (data) {
        toast.success(t("sign_modal.signup_success"));
        // 注册成功后显示消息
        toast.info(t("sign_modal.check_email"));
      }
    } catch (error) {
      toast.error(t("sign_modal.signup_failed"));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card>
        <CardHeader className="text-center">
          <CardTitle className="text-xl">
            {t("sign_modal.sign_up_title")}
          </CardTitle>
          <CardDescription>
            {t("sign_modal.sign_up_description")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6">
            <div className="flex flex-col gap-4">
              {process.env.NEXT_PUBLIC_AUTH_GOOGLE_ENABLED === "true" && (
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => signIn("google", { callbackUrl: window.location.origin })}
                >
                  <SiGoogle className="w-4 h-4 mr-2" />
                  {t("sign_modal.google_sign_in")}
                </Button>
              )}
            </div>

            {process.env.NEXT_PUBLIC_AUTH_EMAIL_ENABLED === "true" && (
              <>
                <div className="relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t after:border-border">
                  <span className="relative z-10 bg-background px-2 text-muted-foreground">
                    {t("sign_modal.or_continue_with")}
                  </span>
                </div>

                <form className="grid gap-6" onSubmit={handleSignUp}>
                  <div className="grid gap-2">
                    <Label htmlFor="email">{t("sign_modal.email")}</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      required
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="password">{t("sign_modal.password")}</Label>
                    <Input 
                      id="password" 
                      type="password" 
                      required 
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="confirm-password">{t("sign_modal.confirm_password")}</Label>
                    <Input 
                      id="confirm-password" 
                      type="password" 
                      required 
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                    />
                  </div>
                  <Button type="submit" className="w-full" disabled={loading}>
                    {loading ? t("sign_modal.signing_up") : t("sign_modal.sign_up")}
                  </Button>
                </form>
              </>
            )}

            <div className="text-center text-sm">
              {t("sign_modal.already_have_account")}{" "}
              <Button 
                variant="link" 
                className="p-0 h-auto" 
                onClick={() => window.location.href = "/loginseo"}
              >
                {t("sign_modal.login")}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
      <div className="text-balance text-center text-xs text-muted-foreground [&_a]:underline [&_a]:underline-offset-4 [&_a]:hover:text-primary">
        {t("sign_modal.terms_agreement")}{" "}
        <a href="/terms-of-service" target="_blank">
          {t("sign_modal.terms_of_service")}
        </a>{" "}
        {t("sign_modal.and")}{" "}
        <a href="/privacy-policy" target="_blank">
          {t("sign_modal.privacy_policy")}
        </a>
        .
      </div>
    </div>
  );
} 