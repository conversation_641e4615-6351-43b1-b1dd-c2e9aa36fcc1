import { NextRequest, NextResponse } from "next/server";

export const runtime = "edge";

export async function POST(req: NextRequest) {
  try {
    const { prompt, content, model } = await req.json();
    
    // 日志记录请求信息，方便调试
    console.log("Generate API request:", { 
      promptLength: prompt?.length,
      contentLength: content?.length, 
      model: model || "not provided" 
    });
    
    if (!prompt || !content) {
      return NextResponse.json(
        { error: "缺少必要参数" },
        { status: 400 }
      );
    }

    // For development testing, we'll simulate a response if no API key is set
    if (!process.env.TOGETHER_API_KEY) {
      console.warn("No TOGETHER_API_KEY set, using mock response");
      
      // Add a delay to simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      return NextResponse.json({
        content: `回答温度：【8/8】\n\n根据你的描述，推断你是 ESFP。ESFP 常被比喻为"表演者型人格"，具有以下鲜明特点：\n\n1. 外向友好：非常喜欢与人交往，享受成为人群焦点的感觉，在社交场合能够迅速活跃起来。\n\n2. 情感丰富：你对情绪非常敏感，在做决策时常常会考虑到别人的情绪，比较看重人际关系的和谐。\n\n3. 实用主义：你偏好具体和实际的事物，而不是抽象的理论，喜欢立即可见的结果。\n\n4. 灵活自由：你喜欢保持选择的开放性，不愿被严格的计划束缚，能够很好地适应变化。\n\n如果你想进一步了解或确认这一结果，可以分享更多关于你在工作环境中的表现、对未来的规划方式、解决问题的方法等方面的信息。`
      });
    }

    // 确定要使用的模型
    let apiModel = "meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8"; // 默认模型
    
    // 如果提供了model参数，根据前缀判断使用哪个API和模型
    const useTogetherAPI = !model || model.startsWith("meta-llama") || model === "skywork";
    
    // 特殊处理不同模型的情况
    if (model) {
      if (model === "gpt-4" || model === "gpt-3.5-turbo") {
        // 如果使用OpenAI的GPT模型，应该调用不同的API端点
        return await callOpenAIAPI(prompt, content, model);
      } else if (model === "doubao") {
        // 如果使用豆包模型，调用相应API
        return await callDouBaoAPI(prompt, content);
      } else if (model === "skywork") {
        apiModel = "sskos/skywork-math"; // Together.ai上的SkyWork模型
      } else if (model.startsWith("meta-llama")) {
        apiModel = model; // 直接使用提供的完整模型名称
      }
    }

    // Call Together AI API (for Llama and other supported models)
    console.log(`Calling Together AI API with model: ${apiModel}`);
    
    const response = await fetch("https://api.together.xyz/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${process.env.TOGETHER_API_KEY}`,
      },
      body: JSON.stringify({
        messages: [
          {
            role: "system",
            content: prompt,
          },
          {
            role: "user",
            content: content,
          },
        ],
        model: apiModel,
        max_tokens: 2048,
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error("Together AI API error:", errorData);
      return NextResponse.json(
        { error: "AI处理失败", details: errorData },
        { status: 500 }
      );
    }

    const data = await response.json();
    return NextResponse.json({
      content: data.choices[0].message.content,
    });
  } catch (error) {
    console.error("Error processing content:", error);
    return NextResponse.json(
      { error: "处理内容失败", message: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

// OpenAI API调用函数
async function callOpenAIAPI(prompt: string, content: string, model: string) {
  try {
    console.log(`Calling OpenAI API with model: ${model}`);
    
    // 模拟响应，实际项目中应该实现真正的OpenAI API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return NextResponse.json({
      content: `[${model} 模拟响应] 您好！我很乐意回答您的问题。您询问的是关于"${content.substring(0, 30)}..."的问题。这是一个很好的问题，我会尽力提供准确和有用的信息。\n\n根据您的问题，我认为...[此处是详细回答]`
    });
    
    // 真实实现应类似：
    /*
    const response = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${process.env.OPENAI_API_KEY}`,
      },
      body: JSON.stringify({
        messages: [
          {
            role: "system",
            content: prompt,
          },
          {
            role: "user",
            content: content,
          },
        ],
        model: model,
        max_tokens: 2048,
      }),
    });
    
    if (!response.ok) {
      throw new Error("OpenAI API error");
    }
    
    const data = await response.json();
    return NextResponse.json({
      content: data.choices[0].message.content,
    });
    */
  } catch (error) {
    console.error("Error calling OpenAI API:", error);
    return NextResponse.json(
      { error: "OpenAI API调用失败", message: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

// 豆包API调用函数
async function callDouBaoAPI(prompt: string, content: string) {
  try {
    console.log("Calling DouBao API");
    
    // 模拟响应，实际项目中应该实现真正的豆包API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return NextResponse.json({
      content: `[豆包模型模拟响应] 您好！感谢您的提问。关于"${content.substring(0, 30)}..."，我可以告诉您以下信息：\n\n首先，这是一个很有深度的问题...[此处是详细回答]`
    });
    
    // 真实实现应根据豆包API文档进行
  } catch (error) {
    console.error("Error calling DouBao API:", error);
    return NextResponse.json(
      { error: "豆包API调用失败", message: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
} 