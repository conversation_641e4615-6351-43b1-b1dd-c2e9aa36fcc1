import CredentialsProvider from "next-auth/providers/credentials";
import GitHubProvider from "next-auth/providers/github";
import GoogleProvider from "next-auth/providers/google";
import { NextAuthConfig } from "next-auth";
import { Provider } from "next-auth/providers/index";
import { User } from "@/types/user";
import { getClientIp } from "@/lib/ip";
import { getIsoTimestr } from "@/lib/time";
import { getUuid } from "@/lib/hash";
import { saveUser } from "@/services/user";
import { signInWithEmail } from "@/lib/supabase";

let providers: Provider[] = [];

// Supabase Email Auth
if (process.env.NEXT_PUBLIC_AUTH_EMAIL_ENABLED === "true") {
  providers.push(
    CredentialsProvider({
      id: "supabase-email",
      name: "Email",
      credentials: {
        email: { label: "Email", type: "email", placeholder: "<EMAIL>" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          const { data, error } = await signInWithEmail(
            credentials.email as string,
            credentials.password as string
          );

          if (error) {
            console.error("Supabase auth error:", error);
            return null;
          }

          if (!data || !data.user) {
            console.error("No user data returned from Supabase");
            return null;
          }

          // 从邮箱创建昵称
          const nickname = data.user.email?.split('@')[0] || '';
          
          // 返回标准化的用户对象
          return {
            id: data.user.id,
            email: data.user.email,
            name: nickname,
            nickname: nickname, // 添加nickname字段
            image: null,
            emailVerified: data.user.email_confirmed_at ? new Date(data.user.email_confirmed_at) : null,
          };
        } catch (error) {
          console.error("Supabase auth error:", error);
          return null;
        }
      }
    })
  );
}

// Google One Tap Auth
if (
  process.env.NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED === "true" &&
  process.env.NEXT_PUBLIC_AUTH_GOOGLE_ID
) {
  providers.push(
    CredentialsProvider({
      id: "google-one-tap",
      name: "google-one-tap",

      credentials: {
        credential: { type: "text" },
      },

      async authorize(credentials, req) {
        const googleClientId = process.env.NEXT_PUBLIC_AUTH_GOOGLE_ID;
        if (!googleClientId) {
          console.log("invalid google auth config");
          return null;
        }

        const token = credentials!.credential;

        const response = await fetch(
          "https://oauth2.googleapis.com/tokeninfo?id_token=" + token
        );
        if (!response.ok) {
          console.log("Failed to verify token");
          return null;
        }

        const payload = await response.json();
        if (!payload) {
          console.log("invalid payload from token");
          return null;
        }

        const {
          email,
          sub,
          given_name,
          family_name,
          email_verified,
          picture: image,
        } = payload;
        if (!email) {
          console.log("invalid email in payload");
          return null;
        }

        const user = {
          id: sub,
          name: [given_name, family_name].join(" "),
          email,
          image,
          emailVerified: email_verified ? new Date() : null,
        };

        return user;
      },
    })
  );
}

// Google Auth
if (
  process.env.NEXT_PUBLIC_AUTH_GOOGLE_ENABLED === "true" &&
  process.env.AUTH_GOOGLE_ID &&
  process.env.AUTH_GOOGLE_SECRET
) {
  providers.push(
    GoogleProvider({
      clientId: process.env.AUTH_GOOGLE_ID,
      clientSecret: process.env.AUTH_GOOGLE_SECRET,
    })
  );
}

// Github Auth
if (
  process.env.NEXT_PUBLIC_AUTH_GITHUB_ENABLED === "true" &&
  process.env.AUTH_GITHUB_ID &&
  process.env.AUTH_GITHUB_SECRET
) {
  providers.push(
    GitHubProvider({
      clientId: process.env.AUTH_GITHUB_ID,
      clientSecret: process.env.AUTH_GITHUB_SECRET,
    })
  );
}

export const providerMap = providers
  .map((provider) => {
    if (typeof provider === "function") {
      const providerData = provider();
      return { id: providerData.id, name: providerData.name };
    } else {
      return { id: provider.id, name: provider.name };
    }
  })
  .filter((provider) => provider.id !== "google-one-tap");

export const authOptions: NextAuthConfig = {
  providers,
  pages: {
    signIn: "/auth/signin",
  },
  callbacks: {
    async signIn({ user, account, profile, email, credentials }) {
      const isAllowedToSignIn = true;
      if (isAllowedToSignIn) {
        return true;
      } else {
        // Return false to display a default error message
        return false;
        // Or you can return a URL to redirect to:
        // return '/unauthorized'
      }
    },
    async redirect({ url, baseUrl }) {
      // Allows relative callback URLs
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      // Allows callback URLs on the same origin
      else if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    },
    async session({ session, token, user }) {
      if (token && token.user && token.user) {
        session.user = token.user;
      }
      return session;
    },
    async jwt({ token, user, account }) {
      // Persist the OAuth access_token and or the user id to the token right after signin
      try {
        if (user) {
          // 所有类型的用户登录都会经过这里
          if (account?.type === "credentials" && account?.provider === "supabase-email") {
            // 针对邮箱登录的处理
            console.log("处理邮箱登录用户:", user);
            
            // 创建用户对象，用于保存到数据库
            const dbUser: User = {
              uuid: user.id,
              email: user.email as string,
              nickname: user.name || (user.email as string)?.split('@')[0] || '',
              avatar_url: user.image || '',
              signin_type: 'credentials',
              signin_provider: 'supabase-email',
              signin_openid: user.id,
              created_at: getIsoTimestr(),
              signin_ip: await getClientIp(),
            };

            try {
              // 保存用户信息到数据库
              const savedUser = await saveUser(dbUser);
              
              // 设置token信息
              token.user = {
                uuid: savedUser.uuid,
                email: savedUser.email,
                nickname: savedUser.nickname,
                avatar_url: savedUser.avatar_url,
                created_at: savedUser.created_at,
              };
            } catch (e) {
              console.error("保存邮箱用户失败:", e);
              
              // 即使保存失败，也设置基本的token信息
              token.user = {
                uuid: user.id,
                email: user.email as string,
                nickname: user.name || (user.email as string)?.split('@')[0] || '',
                avatar_url: user.image || '',
                created_at: new Date().toISOString(),
              };
            }
          } else if (user.email && account) {
            // 第三方登录(如谷歌)的处理
            const dbUser: User = {
              uuid: getUuid(),
              email: user.email,
              nickname: user.name || "",
              avatar_url: user.image || "",
              signin_type: account.type,
              signin_provider: account.provider,
              signin_openid: account.providerAccountId,
              created_at: getIsoTimestr(),
              signin_ip: await getClientIp(),
            };

            try {
              const savedUser = await saveUser(dbUser);

              token.user = {
                uuid: savedUser.uuid,
                email: savedUser.email,
                nickname: savedUser.nickname,
                avatar_url: savedUser.avatar_url,
                created_at: savedUser.created_at,
              };
            } catch (e) {
              console.error("save user failed:", e);
            }
          }
        }
        return token;
      } catch (e) {
        console.error("jwt callback error:", e);
        return token;
      }
    },
  },
};
