import { NextRequest, NextResponse } from "next/server";
import { createApiLog } from "@/models/logs";
import { getMillisecond, getIsoTimestr } from "@/lib/time";
import type { ApiLog } from "@/models/logs";
import { auth } from "@/auth";

// 记录API调用的函数
export async function logApiCall({
  req,
  res,
  startTime,
  error = null,
  requestBody = null,
}: {
  req: NextRequest | Request;
  res: NextResponse | Response;
  startTime: number;
  error?: Error | null;
  requestBody?: any; // 预先捕获的请求体，防止请求体被消费后无法读取
}) {
  const endTime = getMillisecond();
  const duration = endTime - startTime;
  let userUuid: string | undefined = undefined;
  let responseBody = null;
  let creditsAffected = undefined;

  // 获取请求路径
  const url = req instanceof NextRequest ? req.nextUrl : new URL(req.url);
  const path = url.pathname;
  const method = req.method;

  // 创建日志对象
  const logData: ApiLog = {
    api_path: path,
    method: method,
    request_body: requestBody,
    response_status: res.status,
    response_body: null,
    user_id: undefined,
    error_message: error ? error.message : undefined,
    request_duration: duration,
    credits_affected: undefined,
    created_at: getIsoTimestr()
  };

  // 尝试获取当前用户
  try {
    const session = await auth();
    if (session?.user?.uuid) {
      userUuid = session.user.uuid;
      logData.user_id = userUuid;
    }
  } catch (userError) {
    console.error("Error getting user session:", userError);
  }

  // 如果请求体没有预先提供，尝试读取请求体
  if (!requestBody) {
    try {
      if (["POST", "PUT", "PATCH"].includes(method)) {
        // 这里只能在特定场景下读取请求体，因为请求体可能已经被消费
        if (req.headers.get("content-type")?.includes("application/json")) {
          try {
            const reqClone = req instanceof NextRequest ? req.clone() : req.clone();
            logData.request_body = await reqClone.json();
          } catch (bodyError) {
            console.debug("Cannot read request body (may already be consumed)");
          }
        }
      }
    } catch (reqError) {
      console.error("Error processing request:", reqError);
    }
  }

  // 尝试读取响应体
  try {
    const contentType = res.headers.get("content-type");
    if (contentType && contentType.includes("application/json")) {
      try {
        const resClone = res.clone();
        const bodyText = await resClone.text();
        
        if (bodyText) {
          try {
            responseBody = JSON.parse(bodyText);
            logData.response_body = responseBody;
            
            // 检查积分变动 - 支持不同的字段名称
            if (responseBody) {
              if (typeof responseBody === "object") {
                if ("creditsUsed" in responseBody) {
                  creditsAffected = responseBody.creditsUsed;
                } else if ("credits_used" in responseBody) {
                  creditsAffected = responseBody.credits_used;
                } else if ("credits" in responseBody) {
                  creditsAffected = responseBody.credits;
                }
                
                // 记录积分变动
                if (creditsAffected !== undefined) {
                  logData.credits_affected = creditsAffected;
                }
              }
            }
          } catch (parseError) {
            console.error("Error parsing response body:", parseError);
          }
        }
      } catch (textError) {
        console.error("Error reading response text:", textError);
      }
    }
  } catch (resError) {
    console.error("Error processing response:", resError);
  }

  // 保存日志
  try {
    await createApiLog(logData);
    console.log(`API日志已记录: ${path} [${method}] ${res.status}`);
  } catch (dbError) {
    console.error("Failed to create API log:", dbError);
  }
}

// 高阶函数来包装API路由处理程序
export function withApiLogger(handler: (req: NextRequest) => Promise<Response>) {
  return async (req: NextRequest) => {
    const startTime = getMillisecond();
    let response: Response;
    let error = null;
    let requestBody = null;

    // 预先读取请求体，因为请求体只能被读取一次
    try {
      if (["POST", "PUT", "PATCH"].includes(req.method)) {
        const contentType = req.headers.get("content-type") || "";
        if (contentType.includes("application/json")) {
          const reqClone = req.clone();
          requestBody = await reqClone.json();
        }
      }
    } catch (e) {
      console.debug("Failed to pre-read request body:", e);
    }

    try {
      // 执行API处理程序
      response = await handler(req);
    } catch (e) {
      // 捕获错误
      error = e instanceof Error ? e : new Error(String(e));
      console.error("API error:", error);
      
      // 创建错误响应
      response = NextResponse.json(
        { error: "Internal Server Error", message: error.message },
        { status: 500 }
      );
    }

    // 异步记录调用，不阻塞响应
    logApiCall({
      req,
      res: response,
      startTime,
      error,
      requestBody
    }).catch(logError => {
      console.error("Error logging API call:", logError);
    });

    return response;
  };
}

// 使用示例:
// export async function GET(req: NextRequest) {
//   return withApiLogger(async (req) => {
//     // API逻辑
//     return NextResponse.json({ data: "some data" });
//   })(req);
// } 