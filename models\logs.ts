import { getSupabaseClient } from "./db";
import { getIsoTimestr } from "@/lib/time";

export interface ApiLog {
  id?: number;
  api_path: string;
  method: string;
  request_body?: any;
  response_status?: number;
  response_body?: any;
  user_id?: string;
  error_message?: string;
  created_at?: string;
  request_duration?: number;
  credits_affected?: number;
}

export async function createApiLog(logData: ApiLog) {
  const supabase = getSupabaseClient();
  
  // 确保有创建时间
  if (!logData.created_at) {
    logData.created_at = getIsoTimestr();
  }
  
  const { data, error } = await supabase
    .from("api_logs")
    .insert(logData);

  if (error) {
    console.error("Failed to create API log:", error);
    // 不抛出错误，因为日志失败不应该影响主要功能
    return null;
  }

  return data;
}

export async function getApiLogs(
  page: number = 1,
  limit: number = 50,
  filters?: {
    api_path?: string;
    method?: string;
    user_id?: string;
    status?: number;
    start_date?: string;
    end_date?: string;
  }
) {
  if (page < 1) page = 1;
  if (limit <= 0) limit = 50;

  const offset = (page - 1) * limit;
  const supabase = getSupabaseClient();

  let query = supabase
    .from("api_logs")
    .select("*", { count: "exact" });

  // 应用过滤条件
  if (filters) {
    if (filters.api_path) {
      query = query.ilike("api_path", `%${filters.api_path}%`);
    }
    if (filters.method) {
      query = query.eq("method", filters.method);
    }
    if (filters.user_id) {
      query = query.eq("user_id", filters.user_id);
    }
    if (filters.status) {
      query = query.eq("response_status", filters.status);
    }
    if (filters.start_date) {
      query = query.gte("created_at", filters.start_date);
    }
    if (filters.end_date) {
      query = query.lte("created_at", filters.end_date);
    }
  }

  const { data, error, count } = await query
    .order("created_at", { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    console.error("Failed to fetch API logs:", error);
    return { logs: [], count: 0 };
  }

  return { logs: data, count: count || 0 };
}

export async function getApiLogById(id: number) {
  const supabase = getSupabaseClient();
  
  const { data, error } = await supabase
    .from("api_logs")
    .select("*")
    .eq("id", id)
    .single();

  if (error) {
    console.error("Failed to fetch API log:", error);
    return null;
  }

  return data;
} 