// 检查用户积分记录
// 运行: node debug/check-user-credits.js

console.log('🔍 用户积分记录分析\n');

console.log('📊 您的积分记录:');
console.log('记录 1: order_no = "ord_MCwwcjn2MhCb", credits = 0 (扣除记录)');
console.log('记录 2: order_no = "", credits = 0 (扣除记录)\n');

console.log('🔍 问题分析:');
console.log('1. 两次调用了 decreaseCredits 函数');
console.log('2. 第一次: 从付费积分中扣除 (有 order_no)');
console.log('3. 第二次: 从免费积分中扣除 (无 order_no)\n');

console.log('💡 可能的原因:');
console.log('1. API 被调用了两次');
console.log('2. 重复提交请求');
console.log('3. 前端重复点击');
console.log('4. 网络重试机制\n');

console.log('🔧 建议的解决方案:');
console.log('1. 添加防重复提交机制');
console.log('2. 使用事务确保原子性');
console.log('3. 添加请求去重');
console.log('4. 前端禁用重复点击\n');

console.log('📋 请在 Supabase 中运行以下查询来检查:');
console.log('');
console.log('-- 查看用户所有积分记录');
console.log('SELECT id, trans_no, created_at, trans_type, credits, order_no, expired_at');
console.log('FROM credits');
console.log('WHERE user_uuid = \'47618b3f-6be6-473f-93e3-8d624efdc88c\'');
console.log('ORDER BY created_at DESC;');
console.log('');
console.log('-- 查看用户有效积分');
console.log('SELECT id, trans_no, created_at, trans_type, credits, order_no, expired_at');
console.log('FROM credits');
console.log('WHERE user_uuid = \'47618b3f-6be6-473f-93e3-8d624efdc88c\'');
console.log('AND expired_at > NOW()');
console.log('ORDER BY expired_at ASC;');
console.log('');
console.log('-- 计算用户当前积分余额');
console.log('SELECT SUM(credits) as total_credits');
console.log('FROM credits');
console.log('WHERE user_uuid = \'47618b3f-6be6-473f-93e3-8d624efdc88c\'');
console.log('AND expired_at > NOW();');
