"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { supabase } from "@/lib/supabase";
import { useSearchParams } from "next/navigation";
import { FormEvent, useEffect, useState } from "react";
import { toast } from "sonner";
import { useTranslations } from "next-intl";

export const runtime = "edge";

export default function ResetPasswordPage() {
  const t = useTranslations();
  const searchParams = useSearchParams();
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);

  // 验证是否有效的重置密码请求
  useEffect(() => {
    const validateResetRequest = async () => {
      // 检查URL参数中是否包含重置令牌
      // 这会由Supabase自动处理
    };

    validateResetRequest();
  }, []);

  const handleResetPassword = async (e: FormEvent) => {
    e.preventDefault();
    
    if (password !== confirmPassword) {
      toast.error(t("sign_modal.passwords_not_match"));
      return;
    }
    
    setLoading(true);
    
    try {
      // 使用Supabase更新密码
      const { error } = await supabase.auth.updateUser({
        password: password
      });
      
      if (error) {
        toast.error(error.message);
        return;
      }
      
      setSuccess(true);
      toast.success(t("sign_modal.password_reset_success"));
    } catch (error) {
      toast.error(t("sign_modal.password_reset_failed"));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex min-h-svh flex-col items-center justify-center gap-6 bg-muted p-6 md:p-10">
      <div className="flex w-full max-w-sm flex-col gap-6">
        <a href="/" className="flex items-center gap-2 self-center font-medium">
          <div className="flex h-6 w-6 items-center justify-center rounded-md border text-primary-foreground">
            <img src="/logo.png" alt="logo" className="size-4" />
          </div>
          {process.env.NEXT_PUBLIC_PROJECT_NAME}
        </a>
        
        <Card>
          <CardHeader className="text-center">
            <CardTitle className="text-xl">
              {t("sign_modal.set_new_password")}
            </CardTitle>
            <CardDescription>
              {t("sign_modal.set_new_password_description")}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {!success ? (
              <form className="grid gap-6" onSubmit={handleResetPassword}>
                <div className="grid gap-2">
                  <Label htmlFor="password">{t("sign_modal.new_password")}</Label>
                  <Input
                    id="password"
                    type="password"
                    required
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="confirm-password">{t("sign_modal.confirm_password")}</Label>
                  <Input
                    id="confirm-password"
                    type="password"
                    required
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                  />
                </div>
                <Button type="submit" className="w-full" disabled={loading}>
                  {loading 
                    ? t("sign_modal.resetting") 
                    : t("sign_modal.reset_password")}
                </Button>
              </form>
            ) : (
              <div className="grid gap-6">
                <p className="text-center">
                  {t("sign_modal.password_reset_success_message")}
                </p>
                <Button 
                  className="w-full" 
                  onClick={() => window.location.href = "/loginseo"}
                >
                  {t("sign_modal.back_to_login")}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 