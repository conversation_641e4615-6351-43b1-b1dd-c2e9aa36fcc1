// 测试新的积分扣减逻辑
// 运行: node debug/test-credit-deduction.js

const fetch = require('node-fetch');

async function testCreditDeduction() {
  console.log('🧪 测试新的积分扣减逻辑...\n');

  console.log('📋 新的积分扣减逻辑说明:');
  console.log('1. 每次调用都会正常扣减积分');
  console.log('2. 按照 FIFO 原则扣减（优先扣减快过期的积分）');
  console.log('3. 为每个来源的积分创建对应的扣减记录');
  console.log('4. 详细的日志输出\n');

  const testData = {
    prompt: 'Test prompt for new credit deduction logic',
    categoryId: '47618b3f-6be6-473f-93e3-8d624efdc88c', // 使用有效的 UUID
    prompt_id: `test-credit-${Date.now()}`, // 唯一的 prompt_id
    aspect_ratio: '3:4'
  };

  console.log('📤 调用 Replicate API...');
  try {
    const response = await fetch('http://localhost:3000/api/replicate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    });

    console.log('📡 响应状态:', response.status);
    const result = await response.text();

    if (response.ok) {
      console.log('✅ API 调用成功!');
      try {
        const data = JSON.parse(result);
        console.log('📊 返回数据:', JSON.stringify(data, null, 2));
      } catch (e) {
        console.log('📄 响应内容:', result.substring(0, 300) + '...');
      }
    } else {
      console.log('❌ API 调用失败');
      console.log('📄 错误内容:', result);
    }

  } catch (error) {
    console.error('❌ 调用失败:', error.message);
  }

  console.log('\n💡 检查积分记录:');
  console.log('请在 Supabase 中运行以下查询来查看积分扣减详情:');
  console.log('');
  console.log('-- 查看最近的积分记录');
  console.log('SELECT id, trans_no, created_at, trans_type, credits, order_no, expired_at');
  console.log('FROM credits');
  console.log('WHERE user_uuid = \'47618b3f-6be6-473f-93e3-8d624efdc88c\'');
  console.log('AND created_at > NOW() - INTERVAL \'10 minutes\'');
  console.log('ORDER BY created_at DESC;');
  console.log('');
  console.log('-- 查看用户当前积分余额');
  console.log('SELECT SUM(credits) as current_balance');
  console.log('FROM credits');
  console.log('WHERE user_uuid = \'47618b3f-6be6-473f-93e3-8d624efdc88c\'');
  console.log('AND expired_at > NOW();');
  console.log('');
  console.log('-- 按订单分组查看积分使用情况');
  console.log('SELECT order_no, SUM(credits) as total_credits, COUNT(*) as record_count');
  console.log('FROM credits');
  console.log('WHERE user_uuid = \'47618b3f-6be6-473f-93e3-8d624efdc88c\'');
  console.log('GROUP BY order_no');
  console.log('ORDER BY total_credits DESC;');
}

// 运行测试
testCreditDeduction();
