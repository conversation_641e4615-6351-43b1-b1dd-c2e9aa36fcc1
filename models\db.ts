import { createClient } from "@supabase/supabase-js";

export function getSupabaseClient() {
  // 支持多种环境变量名称
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL || "";

  // 支持多种密钥环境变量名称
  let supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || process.env.SUPABASE_ANON_KEY || "";
  if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
    supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  }

  // 即使没有设置环境变量，也创建客户端并返回，防止构建失败
  // 在实际运行时如果缺少有效的URL和密钥，相关API请求会失败，但不会导致整个应用崩溃
  const client = createClient(supabaseUrl, supabaseKey);

  return client;
}
