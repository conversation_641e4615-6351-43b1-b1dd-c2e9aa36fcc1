"use client"

import { useState, useEffect } from "react"
import { HomeIcon, ImageIcon, MessageSquare, Settings, ChevronDown, Bell, Menu, X, CreditCard, Zap, RefreshCw, Sparkles } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useSession, signIn, signOut } from "next-auth/react"
import { toast } from "sonner"
import { cn } from "@/lib/utils"
import { getClientUserCredits } from "@/services/creditClient"
import { useClientUserUuid } from "@/services/userClient"
import { supabase } from "@/lib/supabase"
import { User } from "@supabase/supabase-js"

interface SidebarProps {
  isMobileSidebarOpen: boolean
  toggleMobileSidebar: () => void
}

const Sidebar = ({ isMobileSidebarOpen, toggleMobileSidebar }: SidebarProps) => {
  const { data: session, status } = useSession()
  const [userCredits, setUserCredits] = useState<number | null>(null)
  const [isLoadingCredits, setIsLoadingCredits] = useState(false)
  const userUuid = useClientUserUuid()
  const [supabaseUser, setSupabaseUser] = useState<User | null>(null)

  // 检查Supabase会话
  useEffect(() => {
    const checkSupabaseSession = async () => {
      try {
        const { data } = await supabase.auth.getSession()
        console.log('Supabase会话:', data?.session)
        
        if (data?.session) {
          setSupabaseUser(data.session.user)
        }
      } catch (err) {
        console.error('Supabase会话检查错误:', err)
      }
    }
    
    checkSupabaseSession()
  }, [])

  // Fetch user credits from the service
  const getCreditsForUser = async () => {
    if (!session?.user && !supabaseUser) return null
    
    try {
      setIsLoadingCredits(true)
      
      if (!userUuid) {
        return null
      }
      
      const userCreditsData = await getClientUserCredits(userUuid)
      setUserCredits(userCreditsData.left_credits || 0)
      return userCreditsData.left_credits || 0
    } catch (error) {
      console.error("Error fetching credits:", error)
      return null
    } finally {
      setIsLoadingCredits(false)
    }
  }

  // Refresh credits
  const refreshCredits = async () => {
    toast.info("Refreshing credits...")
    const result = await getCreditsForUser()
    if (result !== null) {
      toast.success(`Credits updated: ${result}`)
    } else {
      toast.error("Failed to update credits")
    }
    return result
  }

  // Fetch credits when session changes or when supabaseUser changes
  useEffect(() => {
    if ((status === "authenticated" && session?.user) || supabaseUser) {
      getCreditsForUser()
    } else {
      setUserCredits(null)
    }
  }, [status, session?.user, supabaseUser])

  // For debugging user data
  useEffect(() => {
    if (session?.user) {
      console.log("User session:", session.user)
    }
    if (supabaseUser) {
      console.log("Supabase user:", supabaseUser)
    }
  }, [session, supabaseUser])

  // Handle login
  const handleLogin = () => {
    signIn("google")
  }

  // Handle logout - 同时处理Supabase和NextAuth登出
  const handleLogout = async () => {
    try {
      // 登出Supabase
      await supabase.auth.signOut()
      // 登出NextAuth
      signOut()
      
      toast.success("Successfully logged out")
    } catch (error) {
      console.error("Error logging out:", error)
      toast.error("Error logging out")
    }
  }

  // Handle upgrade button click
  const handleUpgrade = () => {
    // Will be implemented in the future for payment integration
    toast.info("Upgrade feature coming soon!")
  }

  // Get user initials for avatar fallback
  const getUserInitials = () => {
    // 首先检查是否有Supabase用户
    if (supabaseUser && supabaseUser.user_metadata?.name) {
      const nameParts = supabaseUser.user_metadata.name.split(" ")
      if (nameParts.length >= 2) {
        return `${nameParts[0][0]}${nameParts[1][0]}`.toUpperCase()
      }
      return supabaseUser.user_metadata.name.charAt(0).toUpperCase()
    }
    
    // 其次检查NextAuth用户
    if (!session?.user?.nickname) return "U"
    
    const nameParts = session.user.nickname.split(" ")
    if (nameParts.length >= 2) {
      return `${nameParts[0][0]}${nameParts[1][0]}`.toUpperCase()
    }
    
    return session.user.nickname.charAt(0).toUpperCase()
  }

  // 确定是否已登录 - 检查Supabase或NextAuth会话
  const isAuthenticated = supabaseUser !== null || status === "authenticated"
  
  // 获取用户显示名称 - 优先使用Supabase数据
  const displayName = supabaseUser?.user_metadata?.name || session?.user?.nickname || "User"
  
  // 获取用户电子邮件 - 优先使用Supabase数据
  const userEmail = supabaseUser?.email || session?.user?.email
  
  // 获取用户头像 - 优先使用Supabase数据
  const userAvatar = supabaseUser?.user_metadata?.avatar_url || session?.user?.avatar_url || ""

  return (
    <TooltipProvider>
      {/* Top Navigation Bar */}
      <div className="h-14 border-b border-gray-800 bg-gray-900 flex items-center justify-between px-4 sticky top-0 z-10 pt-2 w-full left-0 right-0">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" className="md:hidden mr-2" onClick={toggleMobileSidebar}>
            {isMobileSidebarOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
          </Button>
          <h1 className="text-xl font-bold text-white">Keling AI</h1>
          <Badge variant="outline" className="ml-2 bg-emerald-900/70 text-emerald-100 border-emerald-700">
            Beta
          </Badge>
        </div>
        
        <div className="flex items-center space-x-3">
          {/* Upgrade Button - Always visible for easy access */}
          <Button 
            size="sm"
            variant="default"
            className="bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white hidden sm:flex items-center gap-1"
            onClick={handleUpgrade}
          >
            <Sparkles size={14} className="mr-1" />
            Upgrade Now
          </Button>

          {/* Credits Display in Top Navigation */}
          {isAuthenticated && (
            <div className="rounded-md border border-emerald-800 bg-gray-800 flex items-center px-2.5 py-1.5">
              <Tooltip>
                <TooltipTrigger asChild>
                  <div 
                    className="flex items-center gap-2 cursor-pointer" 
                    onClick={refreshCredits}
                  >
                    <div className="bg-emerald-700 text-white w-6 h-6 rounded-md flex items-center justify-center">
                      <Zap size={14} />
                    </div>
                    <div className="flex items-center">
                      <span className="text-white font-bold">
                        {isLoadingCredits ? (
                          <span className="inline-block animate-pulse">...</span>
                        ) : (
                          userCredits || 0
                        )}
                      </span>
                      <RefreshCw 
                        size={12} 
                        className={cn(
                          "ml-1 text-gray-400",
                          isLoadingCredits ? "animate-spin" : "hover:text-white"
                        )} 
                      />
                    </div>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Available credits (click to refresh)</p>
                </TooltipContent>
              </Tooltip>
            </div>
          )}
          

          
          {isAuthenticated ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-8 flex items-center gap-2 hover:bg-gray-800 pl-1.5 pr-2.5">
                  <Avatar className="h-8 w-8 ring-2 ring-emerald-500/50">
                    <AvatarImage src={userAvatar} alt={displayName} />
                    <AvatarFallback className="bg-gradient-to-br from-emerald-600 to-emerald-800 text-white text-xs font-medium">
                      {getUserInitials()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex flex-col items-start">
                    <span className="hidden sm:inline-block text-sm font-medium truncate max-w-[100px]">
                      {displayName}
                    </span>
                    {userEmail && (
                      <span className="hidden sm:inline-block text-xs text-gray-400 truncate max-w-[100px]">
                        {userEmail}
                      </span>
                    )}
                  </div>
                  <ChevronDown className="h-4 w-4 text-gray-400" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end">
                <DropdownMenuLabel>
                  <div className="flex flex-col space-y-1">
                    <span>{displayName}</span>
                    {userEmail && (
                      <span className="text-xs text-gray-500">{userEmail}</span>
                    )}
                    <span className="text-xs text-gray-500">
                      {supabaseUser ? "Supabase Auth" : "Google Auth"}
                    </span>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuGroup>
                  <DropdownMenuItem>
                    <CreditCard className="mr-2 h-4 w-4" />
                    <span>Credits</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleUpgrade}>
                    <Sparkles className="mr-2 h-4 w-4" />
                    <span>Upgrade Plan</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Settings className="mr-2 h-4 w-4" />
                    <span>Settings</span>
                  </DropdownMenuItem>
                </DropdownMenuGroup>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout}>
                  <span>Logout</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <Button 
              variant="outline" 
              className="bg-emerald-700 hover:bg-emerald-800 text-white border-emerald-700"
              onClick={handleLogin}
            >
              Sign In
            </Button>
          )}
        </div>
      </div>

      {/* Left Navigation Sidebar - Fixed with only icons */}
      <div className="w-16 bg-gray-900 border-r border-gray-800 flex-shrink-0 hidden md:block fixed h-full top-16 z-10">
        <div className="flex flex-col h-full p-2">
          <div className="flex flex-col items-center space-y-4 py-4">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="w-12 h-12 text-emerald-400">
                  <HomeIcon className="h-6 w-6" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p>Home</p>
              </TooltipContent>
            </Tooltip>
            
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="w-12 h-12 bg-gray-800 text-white">
                  <ImageIcon className="h-6 w-6" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p>AI Images</p>
              </TooltipContent>
            </Tooltip>
            
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="w-12 h-12">
                  <MessageSquare className="h-6 w-6" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p>AI Text</p>
              </TooltipContent>
            </Tooltip>
            
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="w-12 h-12">
                  <Settings className="h-6 w-6" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p>Settings</p>
              </TooltipContent>
            </Tooltip>
          </div>
          
          {/* Remove Credits from sidebar since it's now in the top nav */}
        </div>
      </div>
      
      {/* Mobile Sidebar (Overlay) */}
      {isMobileSidebarOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-20 md:hidden" onClick={toggleMobileSidebar}>
          <div className="w-64 h-full bg-gray-900 border-r border-gray-800" onClick={e => e.stopPropagation()}>
            <div className="flex flex-col h-full p-2">
              <div className="flex flex-col items-start space-y-1 py-2">
                {/* User info section for mobile */}
                {isAuthenticated && (
                  <div className="w-full mb-2 p-3 border-b border-gray-800">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-10 w-10 ring-2 ring-emerald-500/50">
                        <AvatarImage src={userAvatar} alt={displayName} />
                        <AvatarFallback className="bg-gradient-to-br from-emerald-600 to-emerald-800 text-white">
                          {getUserInitials()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex flex-col">
                        <span className="font-medium text-white">{displayName}</span>
                        {userEmail && (
                          <span className="text-xs text-gray-400 truncate max-w-[180px]">{userEmail}</span>
                        )}
                        <span className="text-xs text-gray-400">
                          {supabaseUser ? "Supabase Auth" : "Google Auth"}
                        </span>
                      </div>
                    </div>
                    
                    {/* Mobile upgrade button */}
                    <Button 
                      size="sm"
                      variant="default"
                      className="w-full mt-3 bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white flex items-center justify-center"
                      onClick={handleUpgrade}
                    >
                      <Sparkles size={14} className="mr-1" />
                      Upgrade Now
                    </Button>
                  </div>
                )}
                
                <Button variant="ghost" className="w-full justify-start text-emerald-400">
                  <HomeIcon className="h-5 w-5 mr-2" />
                  <span>Home</span>
                </Button>
                <Button variant="ghost" className="w-full justify-start bg-gray-800 text-white">
                  <ImageIcon className="h-5 w-5 mr-2" />
                  <span>AI Images</span>
                </Button>
                <Button variant="ghost" className="w-full justify-start">
                  <MessageSquare className="h-5 w-5 mr-2" />
                  <span>AI Text</span>
                </Button>
                <Button variant="ghost" className="w-full justify-start">
                  <Settings className="h-5 w-5 mr-2" />
                  <span>Settings</span>
                </Button>
              </div>
              
              {/* No need for credits display here as it's in the top nav */}
            </div>
          </div>
        </div>
      )}
    </TooltipProvider>
  )
}

export default Sidebar

