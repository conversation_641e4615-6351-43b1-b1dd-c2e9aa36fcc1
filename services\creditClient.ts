"use client"

// Client-safe implementation to get user credits
export async function getClientUserCredits(userUuid: string) {
  if (!userUuid) {
    console.log("无法获取积分: 用户ID为空");
    return { left_credits: 0 };
  }
  
  console.log(`尝试获取用户积分, 用户ID: ${userUuid}`);
  
  try {
    // 首先尝试从API获取
    const response = await fetch('/api/user/credits', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      cache: 'no-store'
    });
    
    if (!response.ok) {
      console.error(`API返回错误: ${response.status}`);
      
      // 如果API失败，尝试备用方法 - 直接从数据库查询
      return await getDirectCredits(userUuid);
    }
    
    const data = await response.json();
    console.log("API返回积分数据:", data);
    
    // 如果积分为0，尝试备用方法
    if (!data.left_credits) {
      console.log("API返回积分为0，尝试直接查询");
      const directResult = await getDirectCredits(userUuid);
      return directResult;
    }
    
    return { left_credits: data.left_credits || 0 };
  } catch (error) {
    console.error("获取积分出错:", error);
    
    // 出错时尝试备用方法
    return await getDirectCredits(userUuid);
  }
}

// 备用方法：直接从数据库查询积分
async function getDirectCredits(userUuid: string) {
  try {
    console.log("直接查询用户积分");
    
    // 动态导入supabase client，避免SSR问题
    const { supabase } = await import('@/lib/supabase');
    if (!supabase) {
      console.error("无法加载supabase客户端");
      return { left_credits: 0 };
    }
    
    const now = new Date().toISOString();
    
    // 查询当天有效的积分记录
    const { data, error } = await supabase
      .from('credits')
      .select('*')
      .eq('user_uuid', userUuid)
      .gte('expired_at', now);
    
    if (error) {
      console.error("直接查询积分失败:", error);
      return { left_credits: 0 };
    }
    
    if (!data || data.length === 0) {
      console.log("未找到有效积分记录");
      return { left_credits: 0 };
    }
    
    // 计算总积分
    let totalCredits = 0;
    data.forEach(credit => {
      totalCredits += credit.credits;
    });
    
    console.log(`找到${data.length}条积分记录，总计${totalCredits}积分`);
    return { left_credits: totalCredits };
  } catch (err) {
    console.error("备用积分查询方法出错:", err);
    return { left_credits: 0 };
  }
} 