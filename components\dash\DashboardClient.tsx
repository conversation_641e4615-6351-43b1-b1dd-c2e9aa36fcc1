"use client"

import { useState, useEffect, useLayoutEffect, ReactNode } from "react"
import { Sidebar } from "@/components/dash/sidebar"
import { TopNavbar } from "@/components/dash/top-navbar" 
import { Menu } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"

// Use this to safely run client-side code
const useIsomorphicLayoutEffect = typeof window !== 'undefined' ? useLayoutEffect : useEffect

interface DashboardClientProps {
  children: ReactNode;
}

export default function DashboardClient({ children }: DashboardClientProps) {
  // Initialize with dark mode as default
  const [theme, setTheme] = useState<"light" | "dark">("dark")
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [isMobile, setIsMobile] = useState(false)
  const [isClient, setIsClient] = useState(false)

  // Run once after component mounts on client
  useIsomorphicLayoutEffect(() => {
    setIsClient(true)
    
    // Load theme from localStorage, defaulting to dark
    const savedTheme = localStorage.getItem('theme') as "light" | "dark"
    setTheme(savedTheme || "dark")
    
    // Load sidebar state
    const savedSidebarState = localStorage.getItem('sidebarOpen')
    setSidebarOpen(savedSidebarState ? savedSidebarState === 'true' : true)
    
    // Check if mobile
    const checkIfMobile = () => {
      const isMobileView = window.innerWidth < 768
      setIsMobile(isMobileView)
      // On mobile, close sidebar by default
      if (isMobileView) {
        setSidebarOpen(false)
      }
    }

    checkIfMobile()
    window.addEventListener('resize', checkIfMobile)

    return () => {
      window.removeEventListener('resize', checkIfMobile)
    }
  }, [])

  // 当主题变化时应用到文档上
  useEffect(() => {
    if (!isClient) return
    
    localStorage.setItem('theme', theme)
    document.documentElement.classList.toggle('dark', theme === 'dark')
    
    // 更新页面的元数据
    const metaThemeColor = document.querySelector('meta[name="theme-color"]')
    if (metaThemeColor) {
      metaThemeColor.setAttribute('content', theme === 'dark' ? '#1f2937' : '#ffffff')
    }
  }, [theme, isClient])

  // 保存侧边栏状态到本地存储
  useEffect(() => {
    if (!isClient) return
    
    localStorage.setItem('sidebarOpen', sidebarOpen.toString())
  }, [sidebarOpen, isClient])

  const toggleTheme = () => {
    setTheme(prevTheme => prevTheme === "light" ? "dark" : "light")
  }

  const toggleSidebar = () => {
    setSidebarOpen(prevState => !prevState)
  }

  return (
    <div className="flex h-screen overflow-hidden dark bg-gray-900 text-white">
      {/* Mobile sidebar overlay with improved touch interaction */}
      {isMobile && sidebarOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 touch-none" 
          onClick={() => setSidebarOpen(false)}
          aria-hidden="true"
        />
      )}

      {/* Sidebar - fixed width with no margin/padding causing gaps */}
      <div
        className={`${
          isMobile
            ? `fixed inset-y-0 left-0 z-50 transform ${sidebarOpen ? "translate-x-0" : "-translate-x-full"}`
            : `relative`
        } transition-all duration-300 ease-in-out shrink-0`}
      >
        <Sidebar 
          collapsed={!sidebarOpen && !isMobile}
          onToggle={(collapsed) => setSidebarOpen(!collapsed)}
        />
      </div>

      {/* Main content - fills remaining space with no gaps */}
      <div className="flex flex-col flex-1 min-w-0 transition-all duration-300 ease-in-out bg-gray-800">
        <TopNavbar 
          onToggleSidebar={toggleSidebar} 
          isMobile={isMobile}
          sidebarOpen={sidebarOpen}
        />

        {/* Mobile menu button - enhance visibility */}
        {isMobile && (
          <div className="px-4 py-2 border-b border-gray-700">
            <Button 
              variant="outline" 
              size="sm" 
              className="flex items-center w-full justify-center"
              onClick={toggleSidebar}
            >
              <Menu className="h-4 w-4 mr-2" />
              {sidebarOpen ? "关闭菜单" : "打开菜单"}
            </Button>
          </div>
        )}

        {/* 渲染子组件 */}
        {children}
      </div>
    </div>
  )
} 