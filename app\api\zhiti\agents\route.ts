import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
export const runtime = "edge";

export async function GET(req: NextRequest) {
  try {
    // 获取分页参数（可选）
    const { searchParams } = new URL(req.url);
    const limit = parseInt(searchParams.get("limit") || "100");
    const offset = parseInt(searchParams.get("offset") || "0");
    
    // 获取智能体列表，按创建时间降序排序
    const { data, error, count } = await supabase
      .from("agents")
      .select("id, slug, name, description, model_id, created_at", { count: "exact" })
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);
    
    if (error) {
      console.error("获取智能体列表失败:", error);
      return NextResponse.json(
        { error: "获取智能体列表失败" },
        { status: 500 }
      );
    }
    
    // 返回数据
    return NextResponse.json(data || []);
    
  } catch (error) {
    console.error("处理请求时出错:", error);
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    );
  }
} 