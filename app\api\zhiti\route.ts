import { NextRequest, NextResponse } from "next/server";
import { createClient } from '@supabase/supabase-js';

export const runtime = "edge";

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

export async function POST(req: NextRequest) {
  try {
    const { instruction, userId, categoryId } = await req.json();
    
    if (!instruction || !userId || !categoryId) {
      return NextResponse.json(
        { error: "缺少必要参数" },
        { status: 400 }
      );
    }

    console.log('收到优化请求:', { instruction, userId, categoryId });

    // 设置超时
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

    try {
      // Call Together AI API directly using fetch
      const response = await fetch("https://api.together.xyz/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${process.env.TOGETHER_API_KEY}`,
        },
        body: JSON.stringify({
          messages: [
            {
              role: "system",
              content: "你是一个专业的图片提示词生成优化专家。根据用户提供的指令，生成3个高质量FLUX风格的提示词，每个提示词都应该详细、具体的指令，并且能够产生高质量的图片。直接输出3个提示词，每个提示词占一行，不要添加编号或其他额外内容。",
            },
            {
              role: "user",
              content: instruction,
            },
          ],
          model: "meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8",
          max_tokens: 85314,
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json();
        console.error("Together AI API error:", errorData);
        return NextResponse.json(
          { error: `AI处理失败: ${errorData.error?.message || '未知错误'}` },
          { status: 500 }
        );
      }

      const data = await response.json();
      const optimizedPrompts = data.choices[0].message.content
        .split('\n')
        .filter((p: string) => p.trim())
        .map((prompt: string) => prompt.trim());

      console.log('AI生成的提示词:', optimizedPrompts);

      // 保存到数据库
      const savedPrompts = await Promise.all(
        optimizedPrompts.map(async (optimizedPrompt: string) => {
          const { data: prompt, error } = await supabase
            .from('prompts')
            .insert([{
              keyword: instruction,
              optimized_prompt: optimizedPrompt,
              status: 'completed',
              category_id: categoryId,
              user_id: userId
            }])
            .select()
            .single();

          if (error) {
            console.error('保存提示词失败:', error);
            throw error;
          }

          return prompt;
        })
      );

      console.log('保存的提示词:', savedPrompts);
      
      return NextResponse.json({
        prompts: savedPrompts
      });
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          console.error('API调用超时');
          return NextResponse.json(
            { error: "AI处理超时" },
            { status: 504 }
          );
        }
        console.error('API调用错误:', error);
        return NextResponse.json(
          { error: `API调用错误: ${error.message}` },
          { status: 500 }
        );
      }
      throw error;
    }
  } catch (error) {
    console.error("处理指令失败:", error);
    return NextResponse.json(
      { error: "处理指令失败" },
      { status: 500 }
    );
  }
} 