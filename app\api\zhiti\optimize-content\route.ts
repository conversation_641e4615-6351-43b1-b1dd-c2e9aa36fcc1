import { NextRequest, NextResponse } from "next/server";

export const runtime = "edge";

// 字段标签映射表
const fieldLabels: Record<string, string> = {
  description: "描述",
  targetAudience: "适用人群",
  features: "主要特点",
  faq: "常见问题",
  systemPrompt: "系统提示词",
};

export async function POST(req: NextRequest) {
  try {
    // 解析请求数据
    const { field, content, agentName, prompt, generateMultiple } = await req.json();

    // 检查必要参数
    if (!field || !content) {
      return NextResponse.json(
        { error: "缺少必要参数: field 和 content 是必须的" },
        { status: 400 }
      );
    }

    if (!fieldLabels[field]) {
      return NextResponse.json(
        { error: `不支持的字段类型: ${field}` },
        { status: 400 }
      );
    }

    // 构造提示词
    let systemPrompt = `你是一位专业AI内容优化专家，擅长优化智能体相关内容。`;
    let userPrompt = "";

    // 如果需要生成多个结果
    if (generateMultiple) {
      systemPrompt += `请生成3种不同的优化版本，确保每种版本都有独特的风格和侧重点。以JSON格式返回，格式为: {"results": ["版本1", "版本2", "版本3"]}。只返回JSON对象，不要添加额外的文字。`;
    }

    // 根据不同字段构造具体的优化提示
    switch (field) {
      case "description":
        systemPrompt += `请优化以下智能体的描述，使其更加清晰、简洁，突出主要功能和价值，吸引用户。限制在150字以内。`;
        userPrompt = `智能体名称：${agentName}\n当前描述：${content}\n${prompt ? `优化要求：${prompt}` : ""}`;
        break;
      case "targetAudience":
        systemPrompt += `请优化以下智能体的适用人群描述，明确指出目标用户群体，使表述更加精准。限制在100字以内。`;
        userPrompt = `智能体名称：${agentName}\n当前适用人群：${content}\n${prompt ? `优化要求：${prompt}` : ""}`;
        break;
      case "features":
        systemPrompt += `请优化以下智能体的主要特点，使用简洁、有力的语言，每个特点尽量用一到两句话表达。可以适当使用列表格式。限制在200字以内。`;
        userPrompt = `智能体名称：${agentName}\n当前主要特点：${content}\n${prompt ? `优化要求：${prompt}` : ""}`;
        break;
      case "faq":
        systemPrompt += `请优化以下智能体的常见问题(FAQ)，使问答更加清晰、实用，并保持问答的配对格式。确保问题简洁明了，答案全面实用。`;
        userPrompt = `智能体名称：${agentName}\n当前FAQ：${content}\n${prompt ? `优化要求：${prompt}` : ""}`;
        break;
      case "systemPrompt":
        systemPrompt += `请优化以下智能体的系统提示词，使其指令更加明确、条理清晰，能够更好地指导AI模型的行为。`;
        userPrompt = `智能体名称：${agentName}\n当前系统提示词：${content}\n${prompt ? `优化要求：${prompt}` : ""}`;
        break;
    }

    // 构造消息
    const messages = [
      { role: "system", content: systemPrompt },
      { role: "user", content: userPrompt },
    ];

    // 调用Together API
    const response = await fetch("https://api.together.xyz/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${process.env.TOGETHER_API_KEY}`,
      },
      body: JSON.stringify({
        messages: messages,
        model: "meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8",
        max_tokens: 1500,
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      throw new Error("调用Together API失败");
    }

    // 获取响应内容
    const data = await response.json();
    const responseContent = data.choices[0].message.content.trim();

    // 如果是多结果模式，尝试解析JSON
    if (generateMultiple) {
      try {
        const parsedResponse = JSON.parse(responseContent);
        if (parsedResponse.results && Array.isArray(parsedResponse.results)) {
          return NextResponse.json({ 
            results: parsedResponse.results,
            content: parsedResponse.results[0] || "" // 默认第一个作为单一结果
          });
        }
      } catch (e) {
        console.warn("解析多结果响应失败，将以单一结果返回");
      }
    }

    // 返回优化后的内容（单结果模式或解析失败的后备处理）
    return NextResponse.json({ content: responseContent });
  } catch (error: any) {
    console.error("优化内容API错误:", error);
    return NextResponse.json(
      { error: `处理请求时出错: ${error.message}` },
      { status: 500 }
    );
  }
} 