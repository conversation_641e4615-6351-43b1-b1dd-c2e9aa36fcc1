'use client';

import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import type { Image as ImageType } from '@/models/image';
import { TrashIcon, RefreshCwIcon, ZoomInIcon, DownloadIcon } from 'lucide-react';
import { createClient } from '@supabase/supabase-js';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface MasonryGalleryProps {
  initialImages?: ImageType[];
}

// 创建Supabase客户端
const getSupabaseClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
  return createClient(supabaseUrl, supabaseKey);
};

export default function MasonryGallery({ initialImages = [] }: MasonryGalleryProps) {
  console.log(`MasonryGallery渲染，收到${initialImages.length}张初始图片`);
  if (initialImages.length > 0) {
    console.log('初始图片示例:', { 
      id: initialImages[0].id, 
      url: initialImages[0].image_url,
      有prompt: Boolean(initialImages[0].prompt) 
    });
  }
  
  // 直接使用服务器提供的图片，不需要重新加载
  const [images, setImages] = useState<ImageType[]>(initialImages);
  const [loading, setLoading] = useState(false); // 不再需要初始加载状态
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [offset, setOffset] = useState(initialImages.length);
  const moreLimit = 20; // 每次加载更多时获取的数量
  const [deleteImageId, setDeleteImageId] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState<ImageType | null>(null);
  const [isImageDialogOpen, setIsImageDialogOpen] = useState(false);
  const [imageHeights, setImageHeights] = useState<Record<string, number>>({});
  
  // Container ref for observing resize
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] = useState(0);
  
  // Calculate optimal column count based on container width - 优化列数计算
  const columnCount = useMemo(() => {
    if (containerWidth === 0) return 6; // 默认值更合理
    if (containerWidth >= 1536) return 5; // 2xl
    if (containerWidth >= 1280) return 4; // xl
    if (containerWidth >= 1024) return 4; // lg
    if (containerWidth >= 768) return 3; // md
    return 2; // mobile - 移动端单列效果更好
  }, [containerWidth]);
  
  // Update containerWidth on resize - 更可靠的宽度计算
  useEffect(() => {
    const updateWidth = () => {
      if (containerRef.current) {
        const width = containerRef.current.offsetWidth;
        setContainerWidth(width);
      }
    };
    
    // 即时计算宽度
    updateWidth();
    
    // 等DOM完全渲染后再次计算
    requestAnimationFrame(() => {
      updateWidth();
    });
    
    // 建立resize观察器，确保响应式布局
    const resizeObserver = new ResizeObserver(updateWidth);
    
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }
    
    return () => resizeObserver.disconnect();
  }, []);
  
  // Calculate image heights after they load
  const handleImageLoad = useCallback((image: ImageType, event: React.SyntheticEvent<HTMLImageElement>) => {
    const img = event.currentTarget;
    const aspectRatio = img.naturalWidth / img.naturalHeight;
    const height = 1 / aspectRatio;
    
    setImageHeights(prev => ({
      ...prev,
      [image.id]: height
    }));
  }, []);
  
  // Precompute heights for all images when initialImages are provided
  useEffect(() => {
    if (initialImages.length > 0) {
      // 预加载图片以获取正确的高宽比
      const preloadImages = async () => {
        const promises = initialImages.map(image => {
          return new Promise<void>((resolve) => {
            const img = new Image();
            img.onload = () => {
              const aspectRatio = img.width / img.height;
              const height = 1 / aspectRatio;
              
              setImageHeights(prev => ({
                ...prev,
                [image.id]: height
              }));
              
              resolve();
            };
            img.onerror = () => {
              // 图片加载失败时使用默认比例
              setImageHeights(prev => ({
                ...prev,
                [image.id]: 1
              }));
              resolve();
            };
            img.src = image.image_url;
          });
        });
        
        await Promise.all(promises);
      };
      
      preloadImages();
    }
  }, [initialImages]);
  
  // 改进的瀑布流布局算法 - 优化均衡性
  const imageColumns = useMemo(() => {
    if (!images.length) return Array(Math.max(1, columnCount)).fill([]) as ImageType[][];
    if (columnCount <= 1) return [images] as ImageType[][]; // 移动端单列
    
    // 创建平衡高度的列
    const columns: ImageType[][] = Array(columnCount).fill(null).map(() => [] as ImageType[]);
    const columnHeights = Array(columnCount).fill(0);
    
    // 遍历所有图片，并根据高度比例放入最短的列中
    images.forEach(image => {
      // 对于每个图片，找到当前高度最小的列
      const shortestColumnIndex = columnHeights.indexOf(Math.min(...columnHeights));
      
      // 计算图片的高度比例（默认为1:1）
      const imageHeight = imageHeights[image.id] || 1;
      
      // 将图片添加到最短的列中
      columns[shortestColumnIndex].push(image);
      
      // 更新该列的总高度
      columnHeights[shortestColumnIndex] += imageHeight;
    });
    
    return columns;
  }, [images, columnCount, imageHeights]);
  
  // Fetch more images - 多表查询获取更多图片
  const fetchMoreImages = async () => {
    if (loadingMore) return;
    
    try {
      setLoadingMore(true);
      console.log(`尝试加载更多图片，当前偏移量: ${offset}`);
      
      // 创建Supabase客户端
      const supabase = getSupabaseClient();
      
      // 首先尝试从imagesing表获取更多数据
      console.log("尝试从imagesing表获取更多数据");
      let { data: moreImages, error: moreImagesError } = await supabase
        .from('imagesing')
        .select('*')
        .order('created_at', { ascending: false })
        .range(offset, offset + moreLimit - 1);
      
      if (moreImagesError || !moreImages || moreImages.length === 0) {
        console.log(`imagesing表查询结果: ${moreImagesError ? '出错' : '没有更多数据'}`);
        
        // 如果imagesing表没有数据，尝试从images表获取
        console.log("尝试从images表获取更多数据");
        const { data: regularMoreImages, error: regularMoreImagesError } = await supabase
          .from('images')
          .select('*')
          .order('created_at', { ascending: false })
          .range(offset, offset + moreLimit - 1);
        
        if (regularMoreImagesError) {
          console.error('从images表获取数据出错:', regularMoreImagesError);
        } else if (regularMoreImages && regularMoreImages.length > 0) {
          console.log(`从images表成功获取${regularMoreImages.length}张更多图片`);
          moreImages = regularMoreImages;
        } else {
          console.log('images表也没有更多数据');
        }
      } else {
        console.log(`从imagesing表成功获取${moreImages.length}张更多图片`);
      }
      
      const newImages = moreImages || [];
      console.log(`获取到${newImages.length}张新图片`);
      
      if (newImages.length > 0) {
        setImages(prev => [...prev, ...newImages]);
        setHasMore(newImages.length === moreLimit);
        setOffset(offset + newImages.length);
      } else {
        setHasMore(false);
        toast("没有更多图片了");
      }
    } catch (error) {
      console.error('加载更多图片失败:', error);
      toast.error(error instanceof Error ? error.message : '加载更多图片失败');
    } finally {
      setLoadingMore(false);
    }
  };
  
  // Delete image - 多表删除图片
  const deleteImage = async (id: string) => {
    try {
      console.log(`尝试删除图片: ${id}`);
      // 创建Supabase客户端
      const supabase = getSupabaseClient();
      
      // 先尝试从imagesing表删除
      let deleted = false;
      
      // 从imagesing表删除图片
      const { error: imagesIngError } = await supabase
        .from('imagesing')
        .delete()
        .eq('id', id);
      
      if (imagesIngError) {
        console.log('从imagesing表删除失败，尝试从images表删除');
        
        // 尝试从images表删除
        const { error: imagesError } = await supabase
          .from('images')
          .delete()
          .eq('id', id);
        
        if (imagesError) {
          console.error('从images表删除也失败:', imagesError);
          throw new Error(imagesError.message || '删除图片失败');
        } else {
          deleted = true;
          console.log('从images表成功删除图片');
        }
      } else {
        deleted = true;
        console.log('从imagesing表成功删除图片');
      }
      
      if (deleted) {
        setImages(prev => prev.filter(img => img.id !== id));
        toast.success('图片删除成功');
      } else {
        throw new Error('无法找到要删除的图片');
      }
    } catch (error) {
      console.error('删除图片错误:', error);
      toast.error(error instanceof Error ? error.message : '删除图片失败');
    }
  };

  // Download image
  const downloadImage = async (url: string, prompt: string) => {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      const objectUrl = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = objectUrl;
      link.download = `image-${prompt.substring(0, 20).replace(/[^a-z0-9]/gi, '_')}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      URL.revokeObjectURL(objectUrl);
      toast.success('图片下载成功');
    } catch (error) {
      toast.error('下载图片失败');
    }
  };

  const handleDeleteClick = (id: string) => {
    setDeleteImageId(id);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (deleteImageId) {
      deleteImage(deleteImageId);
    }
    setIsDeleteDialogOpen(false);
  };

  const handleImageClick = (image: ImageType) => {
    setSelectedImage(image);
    setIsImageDialogOpen(true);
  };

  if (loading) {
    return (
      <div className="flex justify-center py-12 min-h-[400px]">
        <div className="flex flex-col items-center">
          <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mb-4"></div>
          <p className="text-gray-400">加载中，请稍候...</p>
        </div>
      </div>
    );
  }

  if (images.length === 0) {
    return (
      <div className="text-center py-12">
        <p>暂无图片</p>
        <p className="text-sm text-gray-400 mt-2">请使用文生图工具创建图片</p>
        <p className="text-xs text-gray-500 mt-4">如果您已经创建了图片，请检查数据库连接</p>
      </div>
    );
  }
  
  // 优化的瀑布流布局渲染
  return (
    <div className="w-full" ref={containerRef} style={{ minHeight: '400px' }}>
      <div 
        className="grid gap-4"
        style={{ 
          display: 'grid',
          gridTemplateColumns: `repeat(${columnCount}, minmax(0, 1fr))`,
          transition: 'all 0.3s ease'
        }}
      >
        {imageColumns.map((column, colIndex) => (
          <div key={colIndex} className="flex flex-col gap-4">
            {column.map((image) => (
              <Card key={image.id} className="overflow-hidden bg-gray-800 border-gray-700 hover:border-gray-500 transition-all duration-200">
                <CardContent className="p-2 relative cursor-pointer group" onClick={() => handleImageClick(image)}>
                  <img 
                    src={image.image_url} 
                    alt={image.prompt}
                    width="600"
                    height={Math.round(600 / (imageHeights[image.id] ? 1/imageHeights[image.id] : 1))}
                    className="w-full h-auto rounded object-cover"
                    onLoad={(e) => handleImageLoad(image, e)}
                    loading="lazy"
                    style={{ 
                      display: 'block',
                      aspectRatio: imageHeights[image.id] ? `${1/imageHeights[image.id]}` : 'auto'
                    }}
                  />
                  <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                    <Button size="sm" variant="outline" className="rounded-full" onClick={(e) => {
                      e.stopPropagation();
                      downloadImage(image.image_url, image.prompt);
                    }}>
                      <DownloadIcon className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="outline" className="rounded-full">
                      <ZoomInIcon className="h-4 w-4" />
                    </Button>
                    <Button 
                      size="sm" 
                      variant="destructive" 
                      className="rounded-full" 
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteClick(image.id);
                      }}
                    >
                      <TrashIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
                <CardFooter className="p-3 flex-col items-start gap-1">
                  <div className="text-sm font-medium line-clamp-2">{image.prompt}</div>
                  <div className="text-xs text-gray-400">
                    {new Date(image.created_at || '').toLocaleDateString()}
                  </div>
                </CardFooter>
              </Card>
            ))}
          </div>
        ))}
      </div>
      
      {hasMore && (
        <div className="mt-8 text-center">
          <Button 
            onClick={fetchMoreImages} 
            disabled={loadingMore}
            variant="outline"
          >
            {loadingMore ? (
              <>
                <RefreshCwIcon className="mr-2 h-4 w-4 animate-spin" />
                正在加载更多图片...
              </>
            ) : (
              "加载更多图片"
            )}
          </Button>
        </div>
      )}
      
      {/* Delete confirmation dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>删除图片</AlertDialogTitle>
            <AlertDialogDescription>
              确定要删除这张图片吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteConfirm}>
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      {/* Image detail dialog */}
      <Dialog open={isImageDialogOpen} onOpenChange={setIsImageDialogOpen}>
        <DialogContent className="max-w-4xl">
          {selectedImage && (
            <>
              <DialogHeader>
                <DialogTitle className="mb-2">{selectedImage.prompt}</DialogTitle>
              </DialogHeader>
              <div className="mt-4">
                <img 
                  src={selectedImage.image_url} 
                  alt={selectedImage.prompt} 
                  className="w-full h-auto rounded-lg"
                />
              </div>
              <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
                <div>
                  <div className="font-semibold">创建于</div>
                  <div>{new Date(selectedImage.created_at || '').toLocaleString()}</div>
                </div>
                {selectedImage.metadata && (
                  <>
                    <div>
                      <div className="font-semibold">尺寸</div>
                      <div>{selectedImage.metadata.width} x {selectedImage.metadata.height}</div>
                    </div>
                    {selectedImage.metadata.seed !== undefined && (
                      <div>
                        <div className="font-semibold">Seed</div>
                        <div>{selectedImage.metadata.seed}</div>
                      </div>
                    )}
                    {selectedImage.metadata.model && (
                      <div>
                        <div className="font-semibold">模型</div>
                        <div>{selectedImage.metadata.model}</div>
                      </div>
                    )}
                  </>
                )}
              </div>
              <div className="mt-4 flex justify-between">
                <Button
                  variant="outline"
                  onClick={() => downloadImage(selectedImage.image_url, selectedImage.prompt)}
                >
                  <DownloadIcon className="mr-2 h-4 w-4" />
                  下载
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => {
                    setIsImageDialogOpen(false);
                    handleDeleteClick(selectedImage.id);
                  }}
                >
                  <TrashIcon className="mr-2 h-4 w-4" />
                  删除
                </Button>
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}