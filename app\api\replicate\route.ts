import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { getUserCredits, decreaseCredits, CreditsTransType } from "@/services/credit";
import { getSupabaseClient } from "@/models/db";
import { withApiLogger } from "@/lib/api-logger";
import { NextRequest } from "next/server";
import Replicate from "replicate";
import { supabase } from "@/lib/supabase";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

// For Cloudflare deployment, must use Edge Runtime
export const runtime = "edge";

// Initialize Replicate client
const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

// Credits cost per image
const CREDITS_PER_IMAGE = 3;

async function handleGenerateRequest(req: Request) {
  try {
    console.log('Starting image generation request...');
    
    // 获取用户会话
    const session = await auth();
    if (!session?.user?.id) {
      console.log('Authentication failed: No user session');
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const body = await req.json();
    console.log('Request parameters:', body);

    const { prompt, width, height, count, categoryId, style, aspect_ratio, prompt_id } = body;

    if (!prompt || !categoryId || !prompt_id) {
      return NextResponse.json(
        { error: "Missing required parameters" },
        { status: 400 }
      );
    }

    // 1. 创建初始数据库记录
    console.log('Creating initial database entry...');
    const { data: promptData, error: createError } = await supabase
      .from('prompts')
      .insert({
        id: prompt_id,
        category_id: categoryId,
        keyword: prompt.slice(0, 100), // 使用提示词的前100个字符作为关键词
        optimized_prompt: prompt,
        status: 'processing',
        image_url: null,
        image_error: null,
        prediction_id: null,
        user_id: session.user.id // 使用会话中的用户ID
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating database entry:', createError);
      return NextResponse.json(
        { error: "Failed to create database entry" },
        { status: 500 }
      );
    }

    console.log('Database entry created:', promptData);

    // 2. 创建 Replicate 预测
    console.log('Creating Replicate prediction...');
    try {
      // 准备输入参数
      const input = {
        prompt: prompt,
        width: width || 768,
        height: height || 1152,
        num_outputs: count || 1,
        aspect_ratio: aspect_ratio || "1:1",
        style: style || "",
        negative_prompt: "",
        scheduler: "K_EULER",
        num_inference_steps: 50,
        guidance_scale: 7.5,
        seed: Math.floor(Math.random() * 1000000)
      };

      console.log('Replicate input:', input);

      // 使用 predictions.create 方法创建预测
      const prediction = await replicate.predictions.create({
        version: "latest",
        input,
        model: "minimax/image-01"
      });

      console.log('Prediction created:', prediction);

      // 3. 更新数据库记录
      console.log('Updating database with prediction ID...');
      const { error: updateError } = await supabase
        .from('prompts')
        .update({ 
          prediction_id: prediction.id,
          status: 'processing'
        })
        .eq('id', prompt_id);

      if (updateError) {
        console.error('Error updating database with prediction ID:', updateError);
        return NextResponse.json(
          { error: "Failed to update database with prediction ID" },
          { status: 500 }
        );
      }

      // 4. 开始轮询预测状态
      let completed = null;
      for (let i = 0; i < 30; i++) { // 最多等待60秒
        const latest = await replicate.predictions.get(prediction.id);
        console.log('Prediction status:', latest.status);
        
        if (latest.status === "succeeded") {
          completed = latest;
          break;
        } else if (latest.status === "failed" || latest.status === "canceled") {
          throw new Error(`Prediction ${latest.status}: ${latest.error || 'Unknown error'}`);
        }
        
        // 等待2秒后重试
        await new Promise((resolve) => setTimeout(resolve, 2000));
      }

      if (!completed) {
        throw new Error('Prediction timed out');
      }

      // 5. 更新数据库记录为完成状态
      console.log('Updating database with completed status...');
      const { error: finalUpdateError } = await supabase
        .from('prompts')
        .update({ 
          status: 'completed',
          image_url: completed.output[0]
        })
        .eq('id', prompt_id);

      if (finalUpdateError) {
        console.error('Error updating database with completed status:', finalUpdateError);
        return NextResponse.json(
          { error: "Failed to update database with completed status" },
          { status: 500 }
        );
      }

      return NextResponse.json({
        status: 'completed',
        imageUrl: completed.output[0]
      });
    } catch (replicateError) {
      console.error('Replicate API error:', replicateError);
      // 更新数据库记录为失败状态
      await supabase
        .from('prompts')
        .update({ 
          status: 'failed',
          image_error: replicateError instanceof Error ? replicateError.message : 'Failed to generate image'
        })
        .eq('id', prompt_id);
      
      return NextResponse.json(
        { error: replicateError instanceof Error ? replicateError.message : "Failed to generate image" },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in image generation:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to generate image" },
      { status: 500 }
    );
  }
}

// Exported POST method, wrapped with withApiLogger
export async function POST(req: NextRequest) {
  return withApiLogger(async (req) => {
    return handleGenerateRequest(req as unknown as Request);
  })(req);
} 