import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { getUserCredits, decreaseCredits, CreditsTransType } from "@/services/credit";
import { getSupabaseClient } from "@/models/db";
import { withApiLogger } from "@/lib/api-logger";
import { NextRequest } from "next/server";
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { supabase } from "@/lib/supabase";

// For Cloudflare deployment, must use Edge Runtime
export const runtime = "edge";

// Initialize R2 client
const s3Client = new S3Client({
  region: process.env.R2_REGION || "auto",
  endpoint: process.env.R2_ENDPOINT || `https://${process.env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: process.env.R2_ACCESS_KEY_ID || process.env.CLOUDFLARE_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY || process.env.CLOUDFLARE_SECRET_ACCESS_KEY || '',
  },
});

// Credits cost per image
const CREDITS_PER_IMAGE = 3;

async function handleGenerateRequest(req: Request) {
  try {
    console.log('Starting image generation request...');

    // 获取用户会话
    const session = await auth();
    if (!session?.user?.id) {
      console.log('Authentication failed: No user session');
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    console.log('Request parameters:', body);

    const { prompt, width = 768, height = 1152, count = 1, categoryId, style = "", aspect_ratio = "3:4", prompt_id } = body;

    if (!prompt || !categoryId || !prompt_id) {
      return NextResponse.json(
        { error: "Missing required parameters" },
        { status: 400 }
      );
    }

    // 检查用户积分
    const userCredits = await getUserCredits(session.user.uuid || session.user.id);
    if (userCredits.left_credits < CREDITS_PER_IMAGE) {
      return NextResponse.json(
        { error: `积分不足，生成图片需要 ${CREDITS_PER_IMAGE} 积分` },
        { status: 402 }
      );
    }

    // 1. 更新或创建数据库记录
    console.log('Updating database entry...');
    const { data: promptData, error: upsertError } = await supabase
      .from('prompts')
      .upsert({
        id: prompt_id,
        category_id: categoryId,
        keyword: prompt.slice(0, 100),
        optimized_prompt: prompt,
        status: 'processing',
        image_url: null,
        image_error: null,
        user_id: session.user.id,
        metadata: {
          width,
          height,
          count,
          style,
          aspect_ratio
        }
      })
      .select()
      .single();

    if (upsertError) {
      console.error('Error updating database entry:', upsertError);
      return NextResponse.json(
        { error: "Failed to update database entry" },
        { status: 500 }
      );
    }

    console.log('Database entry updated:', promptData);

    // 2. 创建 Replicate 预测
    console.log('Creating Replicate prediction...');
    try {
      // 准备输入参数
      const input = {
        prompt: prompt,
        width: width || 768,
        height: height || 1152,
        num_outputs: count || 1,
        aspect_ratio: aspect_ratio || "1:1",
        style: style || "",
        negative_prompt: "",
        scheduler: "K_EULER",
        num_inference_steps: 50,
        guidance_scale: 7.5,
        seed: Math.floor(Math.random() * 1000000)
      };

      console.log('Replicate input:', input);

      // 使用 predictions.create 方法创建预测
      const prediction = await replicate.predictions.create({
        version: "latest",
        input,
        model: "minimax/image-01"
      });

      console.log('Prediction created:', prediction);

      // 3. 更新数据库记录
      console.log('Updating database with prediction ID...');
      const { error: updateError } = await supabase
        .from('prompts')
        .update({ 
          prediction_id: prediction.id,
          status: 'processing'
        })
        .eq('id', prompt_id);

      if (updateError) {
        console.error('Error updating database with prediction ID:', updateError);
        return NextResponse.json(
          { error: "Failed to update database with prediction ID" },
          { status: 500 }
        );
      }

      // 4. 开始轮询预测状态
      let completed = null;
      for (let i = 0; i < 30; i++) { // 最多等待60秒
        const latest = await replicate.predictions.get(prediction.id);
        console.log('Prediction status:', latest.status);
        
        if (latest.status === "succeeded") {
          completed = latest;
          break;
        } else if (latest.status === "failed" || latest.status === "canceled") {
          throw new Error(`Prediction ${latest.status}: ${latest.error || 'Unknown error'}`);
        }
        
        // 等待2秒后重试
        await new Promise((resolve) => setTimeout(resolve, 2000));
      }

      if (!completed) {
        throw new Error('Prediction timed out');
      }

      // 5. 更新数据库记录为完成状态
      console.log('Updating database with completed status...');
      const { error: finalUpdateError } = await supabase
        .from('prompts')
        .update({ 
          status: 'completed',
          image_url: completed.output[0]
        })
        .eq('id', prompt_id);

      if (finalUpdateError) {
        console.error('Error updating database with completed status:', finalUpdateError);
        return NextResponse.json(
          { error: "Failed to update database with completed status" },
          { status: 500 }
        );
      }

      return NextResponse.json({
        status: 'completed',
        imageUrl: completed.output[0]
      });
    } catch (replicateError) {
      console.error('Replicate API error:', replicateError);
      // 更新数据库记录为失败状态
      await supabase
        .from('prompts')
        .update({ 
          status: 'failed',
          image_error: replicateError instanceof Error ? replicateError.message : 'Failed to generate image'
        })
        .eq('id', prompt_id);
      
      return NextResponse.json(
        { error: replicateError instanceof Error ? replicateError.message : "Failed to generate image" },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in image generation:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to generate image" },
      { status: 500 }
    );
  }
}

// Exported POST method, wrapped with withApiLogger
export async function POST(req: NextRequest) {
  return withApiLogger(async (req) => {
    return handleGenerateRequest(req as unknown as Request);
  })(req);
} 