import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { getUserCredits, decreaseCredits, CreditsTransType } from "@/services/credit";
import { withApiLogger } from "@/lib/api-logger";
import { NextRequest } from "next/server";
import Replicate from "replicate";
import { supabase } from "@/lib/supabase";

// For Cloudflare deployment, must use Edge Runtime
export const runtime = "edge";

// Initialize Replicate client
const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

// Credits cost per image
const CREDITS_PER_IMAGE = 0;

async function handleGenerateRequest(req: Request) {
  try {
    console.log('Starting image generation request...');

    // 获取用户会话
    const session = await auth();
    console.log('Session data:', JSON.stringify(session, null, 2));

    if (!session?.user) {
      console.log('Authentication failed: No user session');
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // 获取用户ID，优先使用 id，然后是 uuid
    const userId = session.user.id || session.user.uuid;
    if (!userId) {
      console.log('Authentication failed: No user ID found');
      return NextResponse.json(
        { error: "User ID not found" },
        { status: 401 }
      );
    }

    const body = await req.json();
    console.log('Request parameters:', body);

    const { prompt, categoryId, aspect_ratio = "3:4", prompt_id } = body;

    if (!prompt || !categoryId || !prompt_id) {
      return NextResponse.json(
        { error: "Missing required parameters" },
        { status: 400 }
      );
    }

    // 检查用户积分
    const userCredits = await getUserCredits(userId);
    if (userCredits.left_credits < CREDITS_PER_IMAGE) {
      return NextResponse.json(
        { error: `积分不足，生成图片需要 ${CREDITS_PER_IMAGE} 积分` },
        { status: 402 }
      );
    }

    // 1. 更新或创建数据库记录
    console.log('Updating database entry...');
    const { data: promptData, error: upsertError } = await supabase
      .from('prompts')
      .upsert({
        id: prompt_id,
        category_id: categoryId,
        keyword: prompt.slice(0, 100),
        optimized_prompt: prompt,
        status: 'processing',
        image_url: null,
        image_error: null,
        user_id: userId
      })
      .select()
      .single();

    if (upsertError) {
      console.error('Error updating database entry:', upsertError);
      return NextResponse.json(
        { error: "Failed to update database entry" },
        { status: 500 }
      );
    }

    console.log('Database entry updated:', promptData);

    // 2. 创建 Replicate 预测
    console.log('Creating Replicate prediction...');
    try {
      // 准备输入参数 - 使用 minimax/image-01 模型参数
      const input = {
        prompt: prompt,
        aspect_ratio: aspect_ratio,
        output_format: "png",
        output_quality: 90
      };

      console.log('Replicate input:', input);

      // 使用 minimax/image-01 模型
      const webhookUrl = `${process.env.NEXT_PUBLIC_WEB_URL || 'https://5041-2408-8207-5408-1220-3cfe-8e9-9e40-54ce.ngrok-free.app'}/api/replicate/webhook`;
      console.log('🔗 Webhook URL:', webhookUrl);

      const prediction = await replicate.predictions.create({
        model: "minimax/image-01",
        input: input,
        webhook: webhookUrl,
        webhook_events_filter: ["completed"]
      });

      console.log('Prediction created:', prediction);

      // 3. 更新数据库记录
      console.log('📝 Step 3: Updating database with prediction ID...');
      const { error: updateError } = await supabase
        .from('prompts')
        .update({
          prediction_id: prediction.id,
          status: 'processing'
        })
        .eq('id', prompt_id);

      if (updateError) {
        console.error('❌ Error updating database with prediction ID:', updateError);
        return NextResponse.json(
          { error: "Failed to update database with prediction ID" },
          { status: 500 }
        );
      }
      console.log('✅ Step 3 Complete: Database updated with prediction ID');

      // 4. 扣除积分
      console.log('💰 Step 4: Deducting credits...');
      await decreaseCredits({
        user_uuid: userId,
        trans_type: CreditsTransType.SystemAdd,
        credits: CREDITS_PER_IMAGE
      });
      console.log('✅ Step 4 Complete: Credits deducted');

      // 5. 返回预测ID，让前端轮询
      console.log('📤 Step 5: Returning response to client');
      const response = {
        status: 'processing',
        predictionId: prediction.id,
        message: '图片生成中，请稍候...',
        webhookUrl: `${process.env.NEXT_PUBLIC_WEB_URL || 'http://localhost:3000'}/api/replicate/webhook`
      };
      console.log('✅ Step 5 Complete: Response prepared:', response);

      return NextResponse.json(response);
    } catch (replicateError) {
      console.error('Replicate API error:', replicateError);
      // 更新数据库记录为失败状态
      await supabase
        .from('prompts')
        .update({ 
          status: 'failed',
          image_error: replicateError instanceof Error ? replicateError.message : 'Failed to generate image'
        })
        .eq('id', prompt_id);
      
      return NextResponse.json(
        { error: replicateError instanceof Error ? replicateError.message : "Failed to generate image" },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in image generation:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to generate image" },
      { status: 500 }
    );
  }
}

// Exported POST method, wrapped with withApiLogger
export async function POST(req: NextRequest) {
  return withApiLogger(async (req) => {
    return handleGenerateRequest(req as unknown as Request);
  })(req);
} 