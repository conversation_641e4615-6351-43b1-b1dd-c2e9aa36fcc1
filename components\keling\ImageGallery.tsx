"use client"

import { useState, useEffect } from "react"
import { Loader2, Refresh<PERSON><PERSON>, Upload, Download, Info, Sparkles, Heart, MessageSquare, Share, Copy, Check, X, ChevronDown, Zap } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { toast } from "sonner"
import React from "react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Import next-auth hooks for authentication
import { useSession, signIn } from "next-auth/react"

// Add a dialog for insufficient credits
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"

import ModelSelection from "../modou/ModelSelection";
import DemoData from "../modou/DemoData";
import Results from "../modou/Results";

interface ImageGalleryProps {
  onGenerate?: (images: string[]) => void
}

const ImageGallery = ({ onGenerate }: ImageGalleryProps) => {
  const [selectedAspectRatio, setSelectedAspectRatio] = useState("2:3")
  const [generationCount, setGenerationCount] = useState(4)
  const [prompt, setPrompt] = useState("")
  const [isOptimizing, setIsOptimizing] = useState(false)
  const [optimizedPrompts, setOptimizedPrompts] = useState<string[]>([])
  const [selectedOptimizedPrompt, setSelectedOptimizedPrompt] = useState<string>("")
  const [selectedImage, setSelectedImage] = useState<File | null>(null)
  const [generatedImages, setGeneratedImages] = useState<string[]>([])
  const [isGenerating, setIsGenerating] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<string>("")
  const [selectedModel, setSelectedModel] = useState("flux.schnell")
  
  // Add state for user credits
  const [userCredits, setUserCredits] = useState<number>(0)
  // Add state for the cost per generation
  const [creditCost, setCreditCost] = useState<number>(3)
  // Add state for the recharge dialog
  const [showRechargeDialog, setShowRechargeDialog] = useState<boolean>(false)
  // Add state for login dialog
  const [showLoginDialog, setShowLoginDialog] = useState<boolean>(false)
  // Add state for credits loading
  const [isLoadingCredits, setIsLoadingCredits] = useState(false)
  // Add state for generation history
  const [generationHistory, setGenerationHistory] = useState<any[]>([])
  
  // Get session from next-auth
  const { data: session, status } = useSession()
  
  // 保留这些引用，因为它们在其他地方被使用
  // Reference to the current generation
  const currentGenerationRef = React.useRef<HTMLDivElement>(null)
  // Reference to the scroll container
  const scrollContainerRef = React.useRef<HTMLDivElement>(null)
  const textareaRef = React.useRef<HTMLTextAreaElement>(null)
  const pcTextareaRef = React.useRef<HTMLTextAreaElement>(null)

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/categories', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        })
        
        if (!response.ok) {
          throw new Error(`Error: ${response.status}`)
        }
        
        const data = await response.json()
        if (data.length > 0) {
          setSelectedCategory(data[0].id)
        }
      } catch (error) {
        console.error("Error fetching categories:", error)
      }
    }
    
    fetchCategories()
  }, [])

  // Function to fetch user credits
  const getCreditsForUser = async () => {
    if (status !== "authenticated" || !session?.user) {
      setUserCredits(0)
      return null
    }
    
    try {
      setIsLoadingCredits(true)
      
      const response = await fetch('/api/user/credits', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        cache: 'no-store'
      })
      
      if (!response.ok) {
        throw new Error(`Error: ${response.status}`)
      }
      
      const data = await response.json()
      const credits = data.left_credits || 0
      setUserCredits(credits)
      return credits
    } catch (error) {
      console.error("Error fetching credits:", error)
      setUserCredits(0)
      return null
    } finally {
      setIsLoadingCredits(false)
    }
  }

  // Simplified refresh credits function
  const refreshCredits = async (e?: React.MouseEvent) => {
    if (e) e.stopPropagation();
    toast.info("Refreshing credits...")
    const result = await getCreditsForUser()
    if (result !== null) {
      toast.success(`Credits updated: ${result}`)
    } else {
      toast.error("Failed to update credits")
    }
  }

  // Update effect to fetch credits when session changes
  useEffect(() => {
    if (status === "authenticated" && session?.user) {
      getCreditsForUser()
    } else {
      setUserCredits(0)
    }
  }, [status, session?.user])

  const handleGenerationCountChange = (value: number[]) => {
    setGenerationCount(value[0])
  }

  const getDimensions = (ratio: string) => {
    switch (ratio) {
      case "1:1":
        return { width: 1024, height: 1024 }
      case "2:3":
        return { width: 768, height: 1152 }
      case "3:2":
        return { width: 1152, height: 768 }
      default:
        return { width: 1024, height: 1024 }
    }
  }

  const handleOptimizePrompt = async () => {
    if (!prompt.trim()) {
      toast.error("Please enter a prompt")
      return
    }

    setIsOptimizing(true)
    try {
      const response = await fetch("/api/zhiti", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          instruction: prompt,
        }),
      })

      if (!response.ok) {
        throw new Error("Prompt optimization failed")
      }

      const data = await response.json()
      const prompts = data.content.split("\n").filter((p: string) => p.trim())
      setOptimizedPrompts(prompts)
      toast.success("Prompt optimization successful")
    } catch (error) {
      console.error("Optimization failed:", error)
      toast.error("Prompt optimization failed")
    } finally {
      setIsOptimizing(false)
    }
  }

  const handleOptimizedPromptClick = (optimizedPrompt: string) => {
    setSelectedOptimizedPrompt(optimizedPrompt)
    setPrompt(optimizedPrompt)
  }

  const handleCopyPrompt = (promptText: string) => {
    navigator.clipboard.writeText(promptText)
      .then(() => {
        toast.success("Prompt copied to clipboard")
      })
      .catch((error) => {
        console.error("Failed to copy: ", error)
        toast.error("Failed to copy prompt")
      })
  }

  const handleClearPrompt = () => {
    setPrompt("")
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = 'min-content'
    }
  }
  
  // Adjust textarea height based on content
  const adjustTextareaHeight = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setPrompt(e.target.value)
    
    // Auto resize the textarea
    if (textareaRef.current) {
      // Reset height to get the correct scrollHeight
      textareaRef.current.style.height = 'auto'
      // Set new height based on scrollHeight
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
    }
    
    // Also handle PC textarea auto-resize
    if (pcTextareaRef.current) {
      pcTextareaRef.current.style.height = 'auto'
      pcTextareaRef.current.style.height = `${pcTextareaRef.current.scrollHeight}px`
    }
  }
  
  // Initialize textarea height when component mounts
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
    }
    
    if (pcTextareaRef.current) {
      pcTextareaRef.current.style.height = 'auto'
      pcTextareaRef.current.style.height = `${pcTextareaRef.current.scrollHeight}px`
    }
  }, [])

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setSelectedImage(file)
    }
  }

  // Simplified handle generate function
  const handleGenerate = async () => {
    if (isGenerating || !prompt.trim()) return
    
    // Check if user is logged in
    if (status !== "authenticated") {
      setShowLoginDialog(true)
      return
    }
    
    // Check if user has enough credits
    const requiredCredits = creditCost * generationCount
    if (userCredits < requiredCredits) {
      setShowRechargeDialog(true)
      return
    }
    
    setIsGenerating(true)
    
    // Create a loading placeholder in the history
    const loadingId = `loading-${Date.now()}`
    const loadingGeneration = {
      id: loadingId,
      prompt: prompt,
      images: [],
      timestamp: new Date(),
      isLoading: true
    }
    
    // Add the loading placeholder to history
    setGenerationHistory(prev => [...prev, loadingGeneration])
    // 清空已生成的图片
    setGeneratedImages([])
    
    // 滚动到生成动画区域，使用更强的强制滚动机制
    setTimeout(() => {
      // 尝试多种滚动方法确保在不同浏览器都有效
      const scrollContainer = scrollContainerRef.current;
      if (scrollContainer) {
        // 首先尝试平滑滚动到底部
        scrollContainer.scrollTo({
          top: scrollContainer.scrollHeight,
          behavior: 'smooth'
        });
        
        // 为确保可见，再次使用requestAnimationFrame
        requestAnimationFrame(() => {
          scrollContainer.scrollTop = scrollContainer.scrollHeight;
          
          // 一段时间后再次检查滚动位置
          setTimeout(() => {
            scrollContainer.scrollTop = scrollContainer.scrollHeight;
          }, 300);
        });
      }
      
      // 尝试查找并滚动到特定元素（如果有ID）
      const resultsElement = document.getElementById('generation-results');
      if (resultsElement) {
        resultsElement.scrollIntoView({ behavior: 'smooth', block: 'end' });
      }
    }, 100);
    
    try {
      const dimensions = getDimensions(selectedAspectRatio)
      
      // 准备请求参数
      const requestParams = {
        prompt: prompt,
        width: dimensions.width,
        height: dimensions.height,
        count: generationCount,
        model: selectedModel,
        seed: Math.floor(Math.random() * 1000000),
        categoryId: selectedCategory,
        useCredits: true,
        creditCost: creditCost 
      };
      
      // 使用fetch POST请求
      const fetchResponse = await fetch("/api/pic/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestParams),
      });
      
      // 检查非SSE响应
      const contentType = fetchResponse.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        // 普通JSON响应
        const jsonData = await fetchResponse.json();
        
        if (!fetchResponse.ok) {
          throw new Error(jsonData.error || "生成图片失败");
        }
        
        // 更新积分
        if (jsonData.remainingCredits !== undefined) {
          setUserCredits(jsonData.remainingCredits);
        }
        
        // 提取并处理图片URLs
        try {
          const imageUrls = extractImageUrls(jsonData);
          
          // 更新状态
          setGeneratedImages(imageUrls);
          updateGenerationHistory(loadingId, imageUrls);
          
          // 回调
          if (onGenerate) onGenerate(imageUrls);
          
          toast.success("图片生成成功!");
        } catch (error) {
          console.error("处理图片URL出错:", error);
          toast.error(error instanceof Error ? error.message : "处理生成的图片失败");
          setGenerationHistory(prev => prev.filter(item => item.id !== loadingId));
        }
        
        setIsGenerating(false);
        return;
      }
      
      // 处理SSE流式响应
      await handleStreamResponse(fetchResponse, loadingId);
      
    } catch (error) {
      console.error("Generation failed:", error);
      toast.error(error instanceof Error ? error.message : "Image generation failed");
      
      // Remove the loading state from history on error
      setGenerationHistory(prev => prev.filter(item => item.id !== loadingId));
      setIsGenerating(false);
    }
  }

  // Helper function to extract image URLs from any response format
  const extractImageUrls = (data: any): string[] => {
    if (!data) return [];
    
    // Handle different data formats in priority order
    if (data.r2Urls && Array.isArray(data.r2Urls) && data.r2Urls.length > 0) {
      return data.r2Urls;
    }
    
    if (data.r2Url) {
      return [data.r2Url];
    }
    
    if (data.images && Array.isArray(data.images) && data.images.length > 0) {
      return data.images;
    }
    
    if (data.imageUrls && Array.isArray(data.imageUrls)) {
      return data.imageUrls;
    }
    
    if (data.imageUrl) {
      return [data.imageUrl];
    }
    
    if (Array.isArray(data) && data.length > 0) {
      return data;
    }
    
    return [];
  }

  // Optimized streaming response handler
  const handleStreamResponse = async (response: Response, loadingId: string): Promise<void> => {
    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error("无法创建响应流读取器");
    }
    
    // 解码器
    const decoder = new TextDecoder();
    
    // 存储已生成的图片URL
    const generatedImages: string[] = [];
    
    // 处理流式响应
    let buffer = '';
    let isComplete = false;
    
    const scrollToCurrentTask = () => {
      // 强制滚动到当前生成任务
      const currentTask = document.getElementById('current-generation-task');
      if (currentTask) {
        currentTask.scrollIntoView({ behavior: 'smooth', block: 'center' });
      } else {
        // 备用方案：滚动到结果区域的底部
        const resultsElement = document.getElementById('generation-results');
        if (resultsElement) {
          resultsElement.scrollIntoView({ behavior: 'smooth', block: 'end' });
        } else {
          // 再次备用：滚动容器到底部
          const scrollContainer = scrollContainerRef.current;
          if (scrollContainer) {
            scrollContainer.scrollTo({
              top: scrollContainer.scrollHeight,
              behavior: 'smooth'
            });
          }
        }
      }
    };
    
    while (!isComplete) {
      const { done, value } = await reader.read();
      
      if (done) {
        break;
      }
      
      // 解码并添加到缓冲区
      buffer += decoder.decode(value, { stream: true });
      
      // 处理缓冲区中的每个事件
      const events = buffer.split('\n\n');
      // 保留最后一个不完整的事件
      buffer = events.pop() || '';
      
      for (const eventText of events) {
        if (!eventText.trim()) continue;
        
        // 解析事件和数据
        const lines = eventText.split('\n');
        let eventType = '';
        let eventData = '';
        
        for (const line of lines) {
          if (line.startsWith('event:')) {
            eventType = line.substring(6).trim();
          } else if (line.startsWith('data:')) {
            eventData = line.substring(5).trim();
          }
        }
        
        if (!eventType || !eventData) continue;
        
        try {
          const eventDataObj = JSON.parse(eventData);
          
          switch (eventType) {
            case 'start':
              // 任务启动事件
              console.log('图片生成任务已启动');
              // 添加滚动操作，确保用户可以看到生成中的状态
              setTimeout(scrollToCurrentTask, 300);
              break;
              
            case 'progress':
              // 进度更新事件
              toast.info(`生成进度: ${eventDataObj.message}`, {
                id: `progress-${loadingId}`, // 使用固定ID更新同一个toast
              });
              
              // 更新生成历史记录中的进度
              setGenerationHistory(prev => {
                const newHistory = [...prev];
                const index = newHistory.findIndex(item => item.id === loadingId);
                if (index !== -1) {
                  newHistory[index] = {
                    ...newHistory[index],
                    progress: eventDataObj.progress || 0
                  };
                }
                return newHistory;
              });
              
              // 定期滚动确保视图在当前任务上
              if (Math.random() < 0.3) { // 30%的概率执行滚动，避免过于频繁
                setTimeout(scrollToCurrentTask, 200);
              }
              break;
              
            case 'image': {
              // 获取图片URL - 优先使用R2 URL
              const imageUrl = eventDataObj.r2Url || eventDataObj.imageUrl;
              if (!imageUrl) continue;
              
              // 添加到生成的图片列表
              generatedImages.push(imageUrl);
              
              // 更新UI展示 - 使用函数方式保证拿到最新状态
              setGeneratedImages([...generatedImages]);
              
              // 实时更新历史记录
              updateGenerationHistory(loadingId, [...generatedImages]);
              
              // 计算完成进度
              const completed = eventDataObj.completed || generatedImages.length;
              const total = eventDataObj.total || generationCount;
              
              // 通知，使用固定ID避免多条通知
              toast.success(`已生成第 ${completed} 张图片，共 ${total} 张`, {
                id: `image-progress-${loadingId}`,
              });
              
              // 当图片更新时滚动到当前任务
              setTimeout(scrollToCurrentTask, 200);
              
              break;
            }
              
            case 'complete':
              // 任务完成事件
              console.log('图片生成任务已完成');
              isComplete = true;
              
              // 更新用户积分
              if (eventDataObj.remainingCredits !== undefined) {
                setUserCredits(eventDataObj.remainingCredits);
              }
              
              // 更新历史记录，标记为已完成但保留在当前位置显示
              setGenerationHistory(prev => {
                const newHistory = [...prev];
                const index = newHistory.findIndex(item => item.id === loadingId);
                if (index !== -1) {
                  newHistory[index] = {
                    ...newHistory[index],
                    isLoading: false,
                    images: [...generatedImages], // 确保最新的图片数据
                    showInPlace: true, // 保持在原位置显示
                    pinToBottom: true, // 标记应该显示在底部
                    completedAt: new Date() // 记录完成时间
                  };
                }
                return newHistory;
              });
              
              toast.success("所有图片生成成功!", {
                id: `complete-${loadingId}`,
              });
              
              // 完成后滚动到结果，确保可见
              setTimeout(scrollToCurrentTask, 300);
              
              // 在完成后的一段时间后，允许启动新的生成任务
              setTimeout(() => {
                setIsGenerating(false);
              }, 500);
              
              break;
              
            case 'error':
              // 错误事件
              console.error('生成错误:', eventDataObj.message);
              
              // 如果是部分错误，显示警告而不是错误
              if (eventDataObj.partial && generatedImages.length > 0) {
                toast.warning(`生成了 ${eventDataObj.completed || generatedImages.length} 张图片后遇到错误`);
                isComplete = true;
              } else {
                toast.error(eventDataObj.message || "图片生成失败");
                // 删除加载状态
                setGenerationHistory(prev => prev.filter(item => item.id !== loadingId));
              }
              
              setIsGenerating(false);
              isComplete = true;
              break;
          }
        } catch (e) {
          console.error('处理事件数据出错:', e);
        }
      }
    }
    
    // 流程结束，确保状态更新
    if (isGenerating) {
      setIsGenerating(false);
      
      if (generatedImages.length > 0) {
        toast.success("图片生成成功!");
        // 结束时滚动到最终结果
        setTimeout(scrollToCurrentTask, 800);
      } else {
        toast.error("生成过程意外终止");
        setGenerationHistory(prev => prev.filter(item => item.id !== loadingId));
      }
    }
  }

  // Modified generation history update function
  const updateGenerationHistory = (loadingId: string, imageUrls: string[]) => {
    setGenerationHistory(prev => {
      const newHistory = [...prev];
      // 查找loading状态的项
      const loadingIndex = newHistory.findIndex(item => item.id === loadingId);
      
      if (loadingIndex !== -1) {
        // 更新loading项中的图片数组
        newHistory[loadingIndex] = {
          ...newHistory[loadingIndex],
          images: imageUrls,
          isLoading: imageUrls.length < generationCount, // 只有收到所有图片才完成加载
          pinToBottom: true, // 添加标记表示固定在底部
          showInPlace: true // 确保在原位置显示
        };
      }
      
      return newHistory;
    });
  }

  // Simplified credit display component for reuse
  const CreditsDisplay = ({ isMobile = false, onClick }: { isMobile?: boolean, onClick?: (e: React.MouseEvent) => void }) => (
    <div 
      className={`${isMobile ? 'px-3 py-1' : 'ml-2 px-2 py-1'} bg-amber-900/50 rounded ${isMobile ? 'text-xl' : 'text-sm'} font-bold text-amber-400 border border-amber-700/50 flex items-center cursor-pointer`}
      onClick={onClick}
    >
      <Zap className={`${isMobile ? 'h-4 w-4 mr-1.5' : 'h-3.5 w-3.5 mr-1'}`} />
      {isLoadingCredits ? (
        <span className="inline-block animate-pulse">...</span>
      ) : (
        userCredits
      )}
      <RefreshCw 
        className={`${isMobile ? 'h-3.5 w-3.5 ml-1.5' : 'h-3 w-3 ml-1'} text-amber-300/70 hover:text-amber-300`}
        onClick={refreshCredits}
      />
    </div>
  )

  // Add function to handle recharge
  const handleRecharge = () => {
    // Close the dialog
    setShowRechargeDialog(false)
    // Redirect to payment page or open payment modal
    window.location.href = "/pricing"
  }

  // Simplified generation button component for reuse
  const GenerateButton = ({ isMobile = false }: { isMobile?: boolean }) => {
    const isAuth = status === "authenticated";
    const buttonText = isAuth ? "Generate Now" : "Sign In to Generate";
    const creditsText = `${generationCount * creditCost} Credits`;
    
    return (
      <Button
        className="w-full h-12 bg-gradient-to-r from-emerald-500 to-cyan-500 hover:from-emerald-600 hover:to-cyan-600 text-white font-medium shadow-lg"
        onClick={handleGenerate}
        disabled={isGenerating || !prompt.trim()}
      >
        {isGenerating ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Generating...
          </>
        ) : (
          <>
            {buttonText}
            <span className="ml-2 text-xs bg-white bg-opacity-20 px-1 rounded">{creditsText}</span>
          </>
        )}
      </Button>
    );
  };

  // Simplified login dialog
  const LoginDialog = () => (
    <Dialog open={showLoginDialog} onOpenChange={setShowLoginDialog}>
      <DialogContent className="bg-gray-900 border-gray-700 text-white">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-amber-400">Login Required</DialogTitle>
          <DialogDescription className="text-gray-300">
            Please login to generate images and access all features.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <Card className="bg-gradient-to-r from-gray-800 to-gray-900 border-gray-700 p-6 flex flex-col items-center justify-center shadow-md">
            <div className="text-lg text-white font-medium flex items-center mb-4">Join our creative community</div>
            <div className="text-sm text-gray-300 mb-6 text-center">
              Create an account to access AI image generation, save your creations, and unlock premium features.
            </div>
            <Button 
              className="w-full bg-emerald-600 hover:bg-emerald-700 text-white mb-2"
              onClick={() => {
                setShowLoginDialog(false);
                signIn("google");
              }}
            >
              Sign in with Google
            </Button>
          </Card>
        </div>
        <DialogFooter className="flex flex-col sm:flex-row gap-2">
          <Button variant="ghost" className="text-gray-400 hover:text-gray-200" onClick={() => setShowLoginDialog(false)}>
            Continue Browsing
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );

  // 添加滚动条处理
  useEffect(() => {
    // 防止页面滚动
    document.body.style.overflow = 'hidden';
    
    // 组件卸载时恢复
    return () => {
      document.body.style.overflow = '';
    };
  }, []);

  // PremiumCard component - reusable for both PC and mobile
  const PremiumCard = ({ isMobile = false }: { isMobile?: boolean }) => {
    const isAuth = status === "authenticated";
    
    const handleCardClick = () => {
      isAuth ? setShowRechargeDialog(true) : signIn("google");
    };
    
    const handleCreditsClick = (e: React.MouseEvent) => {
      e.stopPropagation();
      refreshCredits();
    };
    
    return (
      <Card 
        className={`bg-gradient-to-r from-purple-900/70 to-blue-900/70 border-indigo-700 p-4 ${
          isMobile ? 'flex items-center justify-between' : 'flex flex-col items-center justify-center h-24'
        } shadow-md hover:bg-gradient-to-r hover:from-purple-800/70 hover:to-blue-800/70 transition-colors cursor-pointer`}
        onClick={handleCardClick}
      >
        {isMobile ? (
          // Mobile layout
          <>
            <div className="flex flex-col">
              <div className="flex items-center mb-1">
                <Sparkles className="h-4 w-4 mr-1 text-indigo-300" />
                <div className="text-sm text-indigo-300 font-medium">
                  {isAuth ? "UNLOCK PREMIUM CREDITS" : "SIGN IN TO GET STARTED"}
                </div>
              </div>
              <div className="text-xs text-gray-300">
                {isAuth 
                  ? <span>Get <span className="text-indigo-300 font-bold">50+ FREE</span> credits</span>
                  : <span>Login with Google to generate images</span>
                }
              </div>
              <div className="text-xs text-gray-400 mt-1">
                {isAuth 
                  ? "Limited time offer - Subscribe Now"
                  : "No credit card required"
                }
              </div>
            </div>
            <div className="flex flex-col items-end">
              {isAuth && (
                <div 
                  className="px-3 py-1 bg-amber-900/50 rounded text-xl font-bold text-amber-400 border border-amber-700/50 flex items-center cursor-pointer"
                  onClick={handleCreditsClick}
                >
                  <Zap className="h-4 w-4 mr-1.5" />
                  {isLoadingCredits ? (
                    <span className="inline-block animate-pulse">...</span>
                  ) : (
                    userCredits
                  )}
                  <RefreshCw 
                    className="h-3.5 w-3.5 ml-1.5 text-amber-300/70 hover:text-amber-300"
                    onClick={handleCreditsClick}
                  />
                </div>
              )}
            </div>
          </>
        ) : (
          // Desktop layout
          <>
            <div className="flex items-center mb-1">
              <Sparkles className="h-5 w-5 mr-2 text-indigo-300" />
              <div className="text-sm text-indigo-300 font-medium">
                {isAuth ? "UNLOCK PREMIUM CREDITS" : "SIGN IN TO GET STARTED"}
              </div>
              {isAuth && (
                <div 
                  className="ml-2 px-2 py-1 bg-amber-900/50 rounded text-sm font-bold text-amber-400 border border-amber-700/50 flex items-center cursor-pointer"
                  onClick={handleCreditsClick}
                >
                  <Zap className="h-3.5 w-3.5 mr-1" />
                  {isLoadingCredits ? (
                    <span className="inline-block animate-pulse">...</span>
                  ) : (
                    userCredits
                  )}
                  <RefreshCw 
                    className="h-3 w-3 ml-1 text-amber-300/70 hover:text-amber-300"
                    onClick={handleCreditsClick}
                  />
                </div>
              )}
            </div>
            <div className="text-xs flex items-center mt-1 text-gray-300">
              {isAuth 
                ? <span>Get <span className="text-indigo-300 mx-1">50+ FREE</span> credits</span>
                : <span>Login with Google to generate images</span>
              }
            </div>
            <div className="text-xs text-gray-400 mt-1">
              {isAuth 
                ? "Limited time offer - Subscribe Now"
                : "No credit card required"
              }
            </div>
          </>
        )}
      </Card>
    );
  };

  // Recharge Dialog Component
  const RechargeDialog = () => (
    <Dialog open={showRechargeDialog} onOpenChange={setShowRechargeDialog}>
      <DialogContent className="bg-gray-900 border-gray-700 text-white">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-amber-400">Insufficient Credits</DialogTitle>
          <DialogDescription className="text-gray-300">
            You need {generationCount * creditCost} credits to generate {generationCount} {generationCount === 1 ? 'image' : 'images'}, but you only have {userCredits} credits.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <Card className="bg-gradient-to-r from-purple-900/70 to-blue-900/70 border-indigo-700 p-6 flex flex-col items-center justify-center shadow-md">
            <Sparkles className="h-8 w-8 mb-3 text-indigo-300" />
            <div className="text-lg text-indigo-200 font-medium flex items-center mb-2">Recharge Your Credits</div>
            <div className="text-sm flex items-center mb-3 text-gray-200">
              Get <span className="text-indigo-300 mx-1 font-bold">50+ FREE</span> credits with any package
            </div>
            <div className="text-sm text-gray-300 mb-4">Limited time offer - Best Value!</div>
            <Button 
              className="w-full bg-indigo-600 hover:bg-indigo-700 text-white"
              onClick={handleRecharge}
            >
              Recharge Now
            </Button>
          </Card>
        </div>
        <DialogFooter className="flex flex-col sm:flex-row gap-2">
          <Button variant="outline" className="border-gray-600 text-gray-400 hover:text-gray-200" onClick={() => setShowRechargeDialog(false)}>
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );

  // Main render function
  return (
    <div className="flex h-screen overflow-hidden">
      {/* PC Layout */}
      <div className="hidden md:flex w-full h-full overflow-hidden">
        {/* Left Input Sidebar - Now fixed */}
        <div className="w-[400px] border-r border-gray-800 flex flex-col h-full overflow-hidden">
          <div className="p-4 border-b border-gray-800 flex items-center justify-between bg-gray-900">
            <h2 className="text-lg font-bold text-white">AI Image Generator</h2>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                className="bg-emerald-900/70 text-emerald-100 border-emerald-700 hover:bg-emerald-800"
              >
                Ver 1.5
                <span className="ml-2 text-xs bg-emerald-500 text-black px-1 rounded">NEW</span>
              </Button>
            </div>
          </div>

          {/* Content Area - With its own scrollbar */}
          <div 
            className="flex-1 overflow-y-auto scrollbar-hide" 
            ref={scrollContainerRef}
          >
            {/* Creative Description Section */}
            <div className="p-4 border-b border-gray-800 bg-gray-900/50">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  <div className="w-6 h-6 rounded-full bg-emerald-600 flex items-center justify-center mr-2">
                    <span className="text-xs text-white">✓</span>
                  </div>
                  <span className="text-emerald-100 font-medium">Creative Description</span>
                </div>
              </div>

              <Card className="bg-gray-900 border-gray-700 p-4 relative shadow-md">
                <textarea
                  ref={pcTextareaRef}
                  value={prompt}
                  onChange={adjustTextareaHeight}
                  className="w-full bg-transparent border-none text-sm text-gray-300 focus:outline-none resize-none min-h-[120px] pb-14 pr-2 overflow-hidden"
                  placeholder="Describe the image you want to generate..."
                  style={{wordWrap: 'break-word', overflowWrap: 'break-word'}}
                />
                <div className="absolute bottom-2 left-0 right-0 flex justify-between px-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-emerald-400 hover:text-emerald-300"
                    onClick={handleOptimizePrompt}
                    disabled={isOptimizing}
                  >
                    {isOptimizing ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        <span className="font-medium tracking-wide">Enhancing...</span>
                      </>
                    ) : (
                      <>
                        <Sparkles className="mr-2 h-4 w-4" />
                        <span className="font-medium tracking-wide">Enhance Prompt</span>
                      </>
                    )}
                  </Button>
                  
                  {prompt.trim() && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-gray-400 hover:text-red-400"
                      onClick={handleClearPrompt}
                    >
                      <X className="mr-1 h-4 w-4" />
                      <span className="font-medium tracking-wide">Clear</span>
                    </Button>
                  )}
                </div>
              </Card>

              {optimizedPrompts.length > 0 && (
                <div className="mt-4">
                  <div className="flex items-center mb-2">
                    <span className="text-sm text-gray-400">Optimized Prompts:</span>
                  </div>
                  <div className="space-y-2">
                    {optimizedPrompts.map((optimizedPrompt, idx) => (
                      <div 
                        key={idx}
                        className="p-2 rounded-md bg-gray-800 cursor-pointer hover:bg-gray-700 border border-gray-700"
                        onClick={() => handleOptimizedPromptClick(optimizedPrompt)}
                      >
                        <div className="flex justify-between items-start">
                          <p className="text-sm text-gray-300 flex-1 mr-2">
                            {optimizedPrompt}
                          </p>
                          <button
                            className="h-6 w-6 rounded-full text-gray-400 hover:text-emerald-400 hover:bg-emerald-900/30 flex-shrink-0 flex items-center justify-center"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleCopyPrompt(optimizedPrompt);
                            }}
                          >
                            <Copy className="h-3.5 w-3.5" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <ModelSelection selectedModel={selectedModel} setSelectedModel={setSelectedModel} />

              <div className="mt-4 flex flex-wrap gap-2">
                <span className="text-sm text-gray-400">Recommended Tags:</span>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-6 text-xs bg-gray-800 border-gray-700 text-emerald-300 hover:bg-gray-700 hover:text-emerald-200"
                >
                  Chinese Style
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-6 text-xs bg-gray-800 border-gray-700 text-emerald-300 hover:bg-gray-700 hover:text-emerald-200"
                >
                  CG Art
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-6 text-xs bg-gray-800 border-gray-700 text-emerald-300 hover:bg-gray-700 hover:text-emerald-200"
                >
                  Fashion
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-6 text-xs bg-gray-800 border-gray-700 text-emerald-300 hover:bg-gray-700 hover:text-emerald-200"
                >
                  Female
                </Button>
                <Button variant="ghost" size="icon" className="h-6 w-6 text-gray-400 hover:text-emerald-400">
                  <RefreshCw className="h-3 w-3" />
                </Button>
              </div>
            </div>

            {/* Image Ratio Section */}
            <div className="p-4 border-b border-gray-800 bg-gray-900/30">
              <div className="flex items-center mb-2">
                <div className="w-6 h-6 rounded-full bg-emerald-600 flex items-center justify-center mr-2">
                  <span className="text-xs text-white">✓</span>
                </div>
                <span className="text-emerald-100 font-medium">Image Ratio</span>
              </div>
              <RadioGroup
                value={selectedAspectRatio}
                onValueChange={setSelectedAspectRatio}
                className="grid grid-cols-3 gap-2"
              >
                <div>
                  <RadioGroupItem value="1:1" id="1:1" className="peer sr-only" />
                  <Label
                    htmlFor="1:1"
                    className="flex flex-col items-center justify-between rounded-md border-2 border-gray-700 bg-gray-800 p-2 hover:bg-gray-700 hover:text-emerald-400 peer-data-[state=checked]:border-emerald-500 [&:has([data-state=checked])]:border-emerald-500 cursor-pointer"
                  >
                    <span className="text-sm">1:1</span>
                    <span className="text-xs text-gray-400">Square</span>
                  </Label>
                </div>
                <div>
                  <RadioGroupItem value="2:3" id="2:3" className="peer sr-only" />
                  <Label
                    htmlFor="2:3"
                    className="flex flex-col items-center justify-between rounded-md border-2 border-gray-700 bg-gray-800 p-2 hover:bg-gray-700 hover:text-emerald-400 peer-data-[state=checked]:border-emerald-500 [&:has([data-state=checked])]:border-emerald-500 cursor-pointer"
                  >
                    <span className="text-sm">2:3</span>
                    <span className="text-xs text-gray-400">Portrait</span>
                  </Label>
                </div>
                <div>
                  <RadioGroupItem value="3:2" id="3:2" className="peer sr-only" />
                  <Label
                    htmlFor="3:2"
                    className="flex flex-col items-center justify-between rounded-md border-2 border-gray-700 bg-gray-800 p-2 hover:bg-gray-700 hover:text-emerald-400 peer-data-[state=checked]:border-emerald-500 [&:has([data-state=checked])]:border-emerald-500 cursor-pointer"
                  >
                    <span className="text-sm">3:2</span>
                    <span className="text-xs text-gray-400">Landscape</span>
                  </Label>
                </div>
              </RadioGroup>
            </div>

            {/* Generation Count Section */}
            <div className="p-4 border-b border-gray-800 bg-gray-900/30">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  <div className="w-6 h-6 rounded-full bg-emerald-600 flex items-center justify-center mr-2">
                    <span className="text-xs text-white">✓</span>
                  </div>
                  <span className="text-emerald-100 font-medium">Generation Count</span>
                </div>
                <span className="text-sm text-emerald-400">{generationCount}</span>
              </div>
              <Slider
                value={[generationCount]}
                onValueChange={handleGenerationCountChange}
                min={1}
                max={4}
                step={1}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-400 mt-1">
                <span>1 image</span>
                <span>4 images</span>
              </div>
            </div>

            {/* Premium Credits Card */}
            <div className="p-4 pb-1 border-b border-gray-800 bg-gray-900/30">
              <PremiumCard />
            </div>
          </div>
          
          {/* Generate Button */}
          <div className="p-4 pt-2 bg-gray-900 border-t border-gray-800 w-full shadow-lg">
            <GenerateButton />
          </div>
        </div>

        {/* Right Content Area - With its own scrollbar */}
        <div className="flex-1 flex flex-col bg-gray-950 overflow-hidden">
          {/* Generation Results - PC Layout */}
          <div className="flex-1 overflow-y-auto scrollbar-hide p-4 bg-gradient-to-b from-gray-900/40 to-gray-950">
            {/* Default Images Card - Always visible */}
            <DemoData 
              isAuthenticated={status === "authenticated"}
            />

            {/* Generation History */}
            <Results 
              isAuthenticated={status === "authenticated"}
              generationHistory={isGenerating 
                ? [{
                    id: `current-${Date.now()}`,
                    prompt: prompt,
                    images: generatedImages,
                    timestamp: new Date(),
                    isLoading: true,
                    progress: generatedImages.length / generationCount * 100
                  }, 
                  ...generationHistory
                ] 
                : generationHistory
              }
            />
          </div>
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="md:hidden flex flex-col w-full h-full overflow-hidden">
        {/* Top Bar - Fixed */}
        <div className="z-10 p-4 border-b border-gray-800 flex items-center justify-between bg-gray-900">
          <h2 className="text-lg font-bold text-white">AI Image Generator</h2>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              className="bg-emerald-900/70 text-emerald-100 border-emerald-700 hover:bg-emerald-800"
            >
              Ver 1.5
              <span className="ml-2 text-xs bg-emerald-500 text-black px-1 rounded">NEW</span>
            </Button>
          </div>
        </div>

        {/* Scrollable Content - Between fixed elements */}
        <div className="flex-1 overflow-y-auto scrollbar-hide">
          {/* Input Controls */}
          <div className="p-4 border-b border-gray-800 bg-gray-900/50">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <div className="w-6 h-6 rounded-full bg-emerald-600 flex items-center justify-center mr-2">
                  <span className="text-xs text-white">✓</span>
                </div>
                <span className="text-emerald-100 font-medium">Creative Description</span>
              </div>
            </div>

            <Card className="bg-gray-900 border-gray-700 p-4 relative shadow-md">
              <textarea
                ref={textareaRef}
                value={prompt}
                onChange={adjustTextareaHeight}
                className="w-full bg-transparent border-none text-sm text-gray-300 focus:outline-none resize-none min-h-[120px] pb-14 pr-2 overflow-hidden"
                placeholder="Describe the image you want to generate..."
                style={{wordWrap: 'break-word', overflowWrap: 'break-word'}}
              />
              <div className="absolute bottom-2 left-0 right-0 flex justify-between px-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-emerald-400 hover:text-emerald-300"
                  onClick={handleOptimizePrompt}
                  disabled={isOptimizing}
                >
                  {isOptimizing ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      <span className="font-medium tracking-wide">Enhancing...</span>
                    </>
                  ) : (
                    <>
                      <Sparkles className="mr-2 h-4 w-4" />
                      <span className="font-medium tracking-wide">Enhance Prompt</span>
                    </>
                  )}
                </Button>
                
                {prompt.trim() && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-gray-400 hover:text-red-400"
                    onClick={handleClearPrompt}
                  >
                    <X className="mr-1 h-4 w-4" />
                    <span className="font-medium tracking-wide">Clear</span>
                  </Button>
                )}
              </div>
            </Card>

            {optimizedPrompts.length > 0 && (
              <div className="mt-4">
                <div className="flex items-center mb-2">
                  <span className="text-sm text-gray-400">Optimized Prompts:</span>
                </div>
                <div className="space-y-2">
                  {optimizedPrompts.map((optimizedPrompt, idx) => (
                    <div 
                      key={idx}
                      className="p-2 rounded-md bg-gray-800 cursor-pointer hover:bg-gray-700 border border-gray-700"
                      onClick={() => handleOptimizedPromptClick(optimizedPrompt)}
                    >
                      <div className="flex justify-between items-start">
                        <p className="text-sm text-gray-300 flex-1 mr-2">
                          {optimizedPrompt}
                        </p>
                        <button
                          className="h-6 w-6 rounded-full text-gray-400 hover:text-emerald-400 hover:bg-emerald-900/30 flex-shrink-0 flex items-center justify-center"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleCopyPrompt(optimizedPrompt);
                          }}
                        >
                          <Copy className="h-3.5 w-3.5" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <ModelSelection selectedModel={selectedModel} setSelectedModel={setSelectedModel} />

            <div className="mt-4 flex flex-wrap gap-2">
              <span className="text-sm text-gray-400">Recommended Tags:</span>
              <Button
                variant="outline"
                size="sm"
                className="h-6 text-xs bg-gray-800 border-gray-700 text-emerald-300 hover:bg-gray-700 hover:text-emerald-200"
              >
                Chinese Style
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="h-6 text-xs bg-gray-800 border-gray-700 text-emerald-300 hover:bg-gray-700 hover:text-emerald-200"
              >
                CG Art
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="h-6 text-xs bg-gray-800 border-gray-700 text-emerald-300 hover:bg-gray-700 hover:text-emerald-200"
              >
                Fashion
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="h-6 text-xs bg-gray-800 border-gray-700 text-emerald-300 hover:bg-gray-700 hover:text-emerald-200"
              >
                Female
              </Button>
              <Button variant="ghost" size="icon" className="h-6 w-6 text-gray-400 hover:text-emerald-400">
                <RefreshCw className="h-3 w-3" />
              </Button>
            </div>
          </div>

          {/* Image Ratio Section */}
          <div className="p-4 border-b border-gray-800 bg-gray-900/30">
            <div className="flex items-center mb-2">
              <div className="w-6 h-6 rounded-full bg-emerald-600 flex items-center justify-center mr-2">
                <span className="text-xs text-white">✓</span>
              </div>
              <span className="text-emerald-100 font-medium">Image Ratio</span>
            </div>
            <RadioGroup
              value={selectedAspectRatio}
              onValueChange={setSelectedAspectRatio}
              className="grid grid-cols-3 gap-2"
            >
              <div>
                <RadioGroupItem value="1:1" id="1:1" className="peer sr-only" />
                <Label
                  htmlFor="1:1"
                  className="flex flex-col items-center justify-between rounded-md border-2 border-gray-700 bg-gray-800 p-2 hover:bg-gray-700 hover:text-emerald-400 peer-data-[state=checked]:border-emerald-500 [&:has([data-state=checked])]:border-emerald-500 cursor-pointer"
                >
                  <span className="text-sm">1:1</span>
                  <span className="text-xs text-gray-400">Square</span>
                </Label>
              </div>
              <div>
                <RadioGroupItem value="2:3" id="2:3" className="peer sr-only" />
                <Label
                  htmlFor="2:3"
                  className="flex flex-col items-center justify-between rounded-md border-2 border-gray-700 bg-gray-800 p-2 hover:bg-gray-700 hover:text-emerald-400 peer-data-[state=checked]:border-emerald-500 [&:has([data-state=checked])]:border-emerald-500 cursor-pointer"
                >
                  <span className="text-sm">2:3</span>
                  <span className="text-xs text-gray-400">Portrait</span>
                </Label>
              </div>
              <div>
                <RadioGroupItem value="3:2" id="3:2" className="peer sr-only" />
                <Label
                  htmlFor="3:2"
                  className="flex flex-col items-center justify-between rounded-md border-2 border-gray-700 bg-gray-800 p-2 hover:bg-gray-700 hover:text-emerald-400 peer-data-[state=checked]:border-emerald-500 [&:has([data-state=checked])]:border-emerald-500 cursor-pointer"
                >
                  <span className="text-sm">3:2</span>
                  <span className="text-xs text-gray-400">Landscape</span>
                </Label>
              </div>
            </RadioGroup>
          </div>

          {/* Generation Count Section */}
          <div className="p-4 border-b border-gray-800 bg-gray-900/30">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <div className="w-6 h-6 rounded-full bg-emerald-600 flex items-center justify-center mr-2">
                  <span className="text-xs text-white">✓</span>
                </div>
                <span className="text-emerald-100 font-medium">Generation Count</span>
              </div>
              <span className="text-sm text-emerald-400">{generationCount}</span>
            </div>
            <Slider
              value={[generationCount]}
              onValueChange={handleGenerationCountChange}
              min={1}
              max={4}
              step={1}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-gray-400 mt-1">
              <span>1 image</span>
              <span>4 images</span>
            </div>
          </div>

          {/* Premium Card for Mobile */}
          <div className="p-4 pb-1 border-b border-gray-800 bg-gray-900/30">
            <PremiumCard isMobile={true} />
          </div>

          {/* Generation Results - Mobile Layout */}
          <div className="p-4 pb-28 bg-gradient-to-b from-gray-900/40 to-gray-950">
            {/* Default Images Card - Always visible */}
            <DemoData 
              isAuthenticated={status === "authenticated"}
              isMobile
            />

            {/* Generation History for mobile */}
            <Results 
              isAuthenticated={status === "authenticated"}
              generationHistory={isGenerating 
                ? [{
                    id: `current-${Date.now()}`,
                    prompt: prompt,
                    images: generatedImages,
                    timestamp: new Date(),
                    isLoading: true,
                    progress: generatedImages.length / generationCount * 100
                  }, 
                  ...generationHistory
                ] 
                : generationHistory
              }
              isMobile
            />
          </div>
        </div>

        {/* Generate Button at Bottom for Mobile - Fixed */}
        <div className="p-4 pt-2 bg-gray-900 border-t border-gray-800 shadow-lg">
          <GenerateButton isMobile={true} />
        </div>
      </div>

      {/* Add Login Dialog */}
      <LoginDialog />

      {/* Add Recharge Dialog */}
      <Dialog open={showRechargeDialog} onOpenChange={setShowRechargeDialog}>
        <DialogContent className="bg-gray-900 border-gray-700 text-white">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold text-amber-400">Insufficient Credits</DialogTitle>
            <DialogDescription className="text-gray-300">
              You need {generationCount * creditCost} credits to generate {generationCount} {generationCount === 1 ? 'image' : 'images'}, but you only have {userCredits} credits.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Card className="bg-gradient-to-r from-purple-900/70 to-blue-900/70 border-indigo-700 p-6 flex flex-col items-center justify-center shadow-md">
              <Sparkles className="h-8 w-8 mb-3 text-indigo-300" />
              <div className="text-lg text-indigo-200 font-medium flex items-center mb-2">Recharge Your Credits</div>
              <div className="text-sm flex items-center mb-3 text-gray-200">
                Get <span className="text-indigo-300 mx-1 font-bold">50+ FREE</span> credits with any package
              </div>
              <div className="text-sm text-gray-300 mb-4">Limited time offer - Best Value!</div>
              <Button 
                className="w-full bg-indigo-600 hover:bg-indigo-700 text-white"
                onClick={handleRecharge}
              >
                Recharge Now
              </Button>
            </Card>
          </div>
          <DialogFooter className="flex flex-col sm:flex-row gap-2">
            <Button variant="outline" className="border-gray-600 text-gray-400 hover:text-gray-200" onClick={() => setShowRechargeDialog(false)}>
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default ImageGallery 