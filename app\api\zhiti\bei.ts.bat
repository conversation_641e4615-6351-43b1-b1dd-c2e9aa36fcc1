import { NextRequest, NextResponse } from "next/server";

export const runtime = "edge";

export async function POST(req: NextRequest) {
  try {
    const { instruction } = await req.json();
    
    if (!instruction) {
      return NextResponse.json(
        { error: "未提供指令" },
        { status: 400 }
      );
    }

    console.log('收到优化请求:', instruction);

    // 设置超时
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

    try {
      // Call Together AI API directly using fetch
      const response = await fetch("https://api.together.xyz/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${process.env.TOGETHER_API_KEY}`,
        },
        body: JSON.stringify({
          messages: [
            {
              role: "system",
              content: "你是一个专业的图片提示词生成优化专家。根据用户提供的指令，生成3个高质量FLUX风格的提示词，每个提示词都应该详细、具体的指令，并且能够产生高质量的图片。直接输出3个提示词，每个提示词占一行，不要添加编号或其他额外内容。",
            },
            {
              role: "user",
              content: instruction,
            },
          ],
          model: "meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8",
          max_tokens: 85314,
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json();
        console.error("Together AI API error:", errorData);
        return NextResponse.json(
          { error: `AI处理失败: ${errorData.error?.message || '未知错误'}` },
          { status: 500 }
        );
      }

      const data = await response.json();
      console.log('AI响应:', data.choices[0].message.content);
      
      return NextResponse.json({
        content: data.choices[0].message.content,
      });
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          console.error('API调用超时');
          return NextResponse.json(
            { error: "AI处理超时" },
            { status: 504 }
          );
        }
        console.error('API调用错误:', error);
        return NextResponse.json(
          { error: `API调用错误: ${error.message}` },
          { status: 500 }
        );
      }
      throw error;
    }
  } catch (error) {
    console.error("处理指令失败:", error);
    return NextResponse.json(
      { error: "处理指令失败" },
      { status: 500 }
    );
  }
} 