"use client"

import { useSession } from "next-auth/react";

// Client-side version to get user UUID - safe to use in client components
export function useClientUserUuid() {
  const { data: session } = useSession();
  return session?.user?.uuid || "";
}

// Client-side function to check if user is authenticated
export function useIsAuthenticated() {
  const { status } = useSession();
  return status === "authenticated";
} 