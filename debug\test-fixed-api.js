// 测试修复后的 API
// 运行: node debug/test-fixed-api.js

const fetch = require('node-fetch');

async function testFixedAPI() {
  console.log('🧪 测试修复后的 Replicate API...\n');

  try {
    const response = await fetch('http://localhost:3000/api/replicate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: 'A beautiful sunset over mountains, photorealistic',
        categoryId: '47618b3f-6be6-473f-93e3-8d624efdc88c', // 使用有效的 UUID
        prompt_id: `47618b3f-6be6-473f-93e3-8d624efdc8${Math.floor(Math.random() * 100).toString().padStart(2, '0')}`, // 生成有效的 UUID
        aspect_ratio: '3:4'
      })
    });

    console.log('📡 响应状态:', response.status);
    console.log('📡 响应头:', Object.fromEntries(response.headers.entries()));

    const responseText = await response.text();
    console.log('📡 响应内容:', responseText);

    if (response.ok) {
      try {
        const data = JSON.parse(responseText);
        console.log('✅ API 调用成功!');
        console.log('📊 返回数据:', JSON.stringify(data, null, 2));
      } catch (parseError) {
        console.log('⚠️  响应不是有效的 JSON:', parseError.message);
      }
    } else {
      console.log('❌ API 调用失败');
      if (response.status === 401) {
        console.log('💡 提示: 需要用户登录');
      } else if (response.status === 500) {
        console.log('💡 提示: 服务器内部错误，检查日志');
      }
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.log('💡 确保开发服务器正在运行 (npm run dev)');
  }
}

// 运行测试
testFixedAPI();
