import { NextResponse } from "next/server";
import { getSupabaseClient } from "@/models/db";
import type { Category } from "@/models/category";
export const runtime = "edge";

// Database schema type (snake_case)
interface CategoryDB {
  id: string;
  name: string;
  slug: string;
  description: string;
  icon?: string;
  seo_title?: string;
  seo_description?: string;
  generator_config?: any;
  shares_config?: any;
  faq_config?: any;
  created_at: string;
  updated_at: string;
}

export async function GET() {
  try {
    const supabase = getSupabaseClient();
    
    const { data, error } = await supabase
      .from('categories')
      .select('id, name, slug')
      .order('name', { ascending: true });
    
    if (error) {
      throw error;
    }
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching categories:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to fetch categories" },
      { status: 500 }
    );
  }
}

// POST - Create a new category
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const supabase = getSupabaseClient();

    // Convert camelCase to snake_case for database
    const dbData = {
      name: body.name,
      slug: body.slug,
      description: body.description,
      icon: body.icon,
      seo_title: body.seoTitle,
      seo_description: body.seoDescription,
      generator_config: body.generatorConfig || {},
      shares_config: body.sharesConfig || {},
      faq_config: body.faqConfig || {}
    };

    const { data, error } = await supabase
      .from("categories")
      .insert(dbData)
      .select()
      .single();

    if (error) {
      console.error("Error creating category:", error);
      
      // Check for unique constraint violation
      if (error.code === "23505") {
        return NextResponse.json(
          { error: "A category with this slug already exists" },
          { status: 409 }
        );
      }
      
      return NextResponse.json(
        { error: "Failed to create category" },
        { status: 500 }
      );
    }

    // Format the response
    const formattedCategory: Category = {
      id: data.id,
      name: data.name,
      slug: data.slug,
      description: data.description,
      icon: data.icon,
      seoTitle: data.seo_title,
      seoDescription: data.seo_description,
      generatorConfig: data.generator_config,
      sharesConfig: data.shares_config,
      faqConfig: data.faq_config,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    };

    return NextResponse.json(formattedCategory, { status: 201 });
  } catch (error) {
    console.error("Error creating category:", error);
    return NextResponse.json(
      { error: "Failed to create category" },
      { status: 500 }
    );
  }
} 