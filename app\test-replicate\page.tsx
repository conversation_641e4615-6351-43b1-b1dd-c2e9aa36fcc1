"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { toast } from 'sonner'

export default function TestReplicatePage() {
  const [prompt, setPrompt] = useState('A beautiful sunset over mountains, photorealistic')
  const [isGenerating, setIsGenerating] = useState(false)
  const [result, setResult] = useState<any>(null)

  const testAPI = async () => {
    setIsGenerating(true)
    setResult(null)

    try {
      const response = await fetch('/api/replicate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt,
          categoryId: 'test-category',
          prompt_id: `test-${Date.now()}`,
          width: 768,
          height: 1152,
          count: 1,
          aspect_ratio: '3:4',
          style: 'photorealistic'
        })
      })

      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'API 调用失败')
      }

      setResult(data)
      toast.success('API 调用成功')
    } catch (error) {
      console.error('Test failed:', error)
      toast.error(`测试失败: ${error instanceof Error ? error.message : '未知错误'}`)
      setResult({ error: error instanceof Error ? error.message : '未知错误' })
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>Replicate API 测试</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              提示词
            </label>
            <Input
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="输入图片描述..."
            />
          </div>
          
          <Button 
            onClick={testAPI} 
            disabled={isGenerating || !prompt.trim()}
            className="w-full"
          >
            {isGenerating ? '测试中...' : '测试 API'}
          </Button>

          {result && (
            <div className="mt-6">
              <h3 className="text-lg font-semibold mb-2">API 响应:</h3>
              <pre className="bg-gray-100 p-4 rounded-lg overflow-auto text-sm">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
