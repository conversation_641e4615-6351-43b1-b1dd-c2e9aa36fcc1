"use client"
import { useEffect, useState } from 'react';
import { toast } from "sonner";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import Image from 'next/image';
import { Loader2 } from "lucide-react";
import { Heart, Download, MessageSquare, Share, X, ImageIcon, Fullscreen } from "lucide-react";

// Define the type for the data returned from the API
interface ImagesingItem {
  id: string;
  user_id: string;
  created_at: string;
  image_url: string;
  prompt?: string;
  title?: string;
  // Add other fields from your imagesing table as needed
}

// Define a type for generation history items
interface GenerationHistoryItem {
  id: string;
  prompt: string;
  images: string[];
  timestamp: Date;
  isLoading?: boolean;
  progress?: number;
  showInPlace?: boolean;
}

interface ResultsProps {
  userId?: string;
  limit?: number;
  isAuthenticated?: boolean;
  generationHistory?: GenerationHistoryItem[];
  isMobile?: boolean;
  loading?: boolean;
  debug?: boolean;
  disableAutoFetch?: boolean;
}

export default function Results({ 
  userId, 
  limit = 10, 
  isAuthenticated,
  generationHistory = [],
  isMobile = false,
  loading = false,
  debug = false,
  disableAutoFetch = false
}: ResultsProps) {
  const [data, setData] = useState<ImagesingItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(!disableAutoFetch);
  const [error, setError] = useState<string | null>(null);

  // Function to parse image URL which might be a JSON string array
  const parseImageUrl = (imageUrl: string): string => {
    try {
      const parsed = JSON.parse(imageUrl);
      // If it's an array, return the first item
      if (Array.isArray(parsed) && parsed.length > 0) {
        return parsed[0];
      }
      // If it's a string after parsing (unlikely but possible), return it
      if (typeof parsed === 'string') {
        return parsed;
      }
      // Fallback
      return '';
    } catch (e) {
      // If it's not valid JSON, return the original string
      return imageUrl;
    }
  };

  useEffect(() => {
    // Skip fetching if disableAutoFetch is true
    if (disableAutoFetch) {
      setIsLoading(false);
      return;
    }
    
    const fetchImages = async () => {
      try {
        setIsLoading(true);
        const targetUserId = userId || '47618b3f-6be6-473f-93e3-8d624efdc88c';
        
        const response = await fetch(`/api/pic/imagesing?userId=${targetUserId}&limit=${limit}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache'
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const result = await response.json();
        setData(result);
      } catch (err) {
        console.error('Failed to fetch images:', err);
        setError('Failed to load images. Please try again later.');
        toast.error("Failed to load images. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchImages();
  }, [userId, limit, disableAutoFetch]);

  // If loading and no generation history provided
  if (isLoading && generationHistory.length === 0) {
    return (
      <div className="space-y-6">
        {Array(3).fill(0).map((_, index) => (
          <Card key={index} className="overflow-hidden rounded-lg shadow-lg bg-gray-900 border-gray-800">
            {/* Author Header */}
            <div className="p-4 flex items-center justify-between border-b border-gray-800">
              <div className="flex items-center">
                <Skeleton className="w-10 h-10 rounded-full mr-3" />
                <div>
                  <Skeleton className="h-4 w-32 mb-2" />
                  <Skeleton className="h-3 w-24" />
                </div>
              </div>
              <Skeleton className="h-6 w-20 rounded-full" />
            </div>

            {/* Prompt */}
            <div className="px-4 py-3 border-b border-gray-800">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3 mt-2" />
            </div>

            {/* Images Grid */}
            <div className="border-t border-dashed border-gray-700 py-1.5 px-2">
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 p-2 bg-gray-950">
                {Array(4).fill(0).map((_, i) => (
                  <Skeleton key={i} className="aspect-square w-full rounded-md" />
                ))}
              </div>
            </div>

            {/* Actions */}
            <div className="p-4 flex items-center justify-between border-t border-gray-800">
              <div className="flex items-center space-x-6">
                <Skeleton className="h-8 w-16 rounded" />
                <Skeleton className="h-8 w-16 rounded" />
                <Skeleton className="h-8 w-8 rounded" />
              </div>
              <Skeleton className="h-9 w-20 rounded" />
            </div>
          </Card>
        ))}
      </div>
    );
  }

  // If there's an error and no generation history
  if (error && generationHistory.length === 0 && !disableAutoFetch) {
    return (
      <Card className="overflow-hidden rounded-lg shadow-lg bg-gray-900 border-gray-800 p-8">
        <div className="flex flex-col items-center justify-center text-center">
          <div className="w-16 h-16 bg-red-900/20 rounded-full flex items-center justify-center mb-4">
            <X className="h-8 w-8 text-red-500" />
          </div>
          <p className="text-red-500 mb-4 font-medium">{error}</p>
          <Button 
            onClick={() => window.location.reload()} 
            variant="outline"
            className="bg-gray-800 border-gray-700 text-gray-200 hover:bg-gray-700"
          >
            Try Again
          </Button>
        </div>
      </Card>
    );
  }

  // If there's no history to display
  if (data.length === 0 && generationHistory.length === 0) {
    return (
      <Card className="overflow-hidden rounded-lg shadow-lg bg-gray-900 border-gray-800 p-8">
        <div className="flex flex-col items-center justify-center text-center">
          <div className="w-16 h-16 bg-gray-800 rounded-full flex items-center justify-center mb-4">
            <ImageIcon className="h-8 w-8 text-gray-500 opacity-50" />
          </div>
          <p className="text-gray-500 mb-4 font-medium">No image history found</p>
          <p className="text-gray-600 text-sm max-w-md">
            Generated images will appear here. Try generating some images to get started.
          </p>
        </div>
      </Card>
    );
  }

  // If showing generation history
  if (generationHistory.length > 0) {
    return (
      <div className="space-y-6">
        {generationHistory.map((item, index) => (
          <Card
            key={item.id}
            className={`overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-shadow bg-gray-900 border-gray-800 ${
              index % 5 === 0 ? 'border-2 border-emerald-500' : ''
            }`}
          >
            {/* Author Header */}
            <div className="p-4 flex items-center justify-between border-b border-gray-800 bg-gray-900">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-gray-700 overflow-hidden mr-3 border-2 border-emerald-500/30">
                  <div className="w-full h-full flex items-center justify-center text-emerald-500 font-bold">
                    AI
                  </div>
                </div>
                <div>
                  <div className="font-semibold text-white">AI Generation</div>
                  <div className="text-sm text-emerald-300">
                    {item.isLoading ? 'Processing...' : `${item.images.length} images`}
                  </div>
                </div>
              </div>
              <div className="text-xs text-gray-400 bg-gray-800 px-2 py-1 rounded-full">
                {new Date(item.timestamp).toLocaleDateString()} {new Date(item.timestamp).toLocaleTimeString().slice(0, 5)}
              </div>
            </div>

            {/* Prompt */}
            <div className="px-4 py-3 border-b border-gray-800 bg-gray-900/70">
              <p className="text-sm text-gray-300">{item.prompt || "No prompt provided"}</p>
            </div>

            {/* Images Grid */}
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 p-2 bg-gray-950">
              {item.images && item.images.length > 0 && 
                item.images.slice(0, 4).map((image, idx) => (
                  <div key={idx} className="relative aspect-[3/4] group overflow-hidden rounded-lg">
                    <img
                      src={image}
                      alt={`Generated image ${idx + 1}`}
                      className="w-full h-full object-cover rounded-lg"
                    />
                    <div className="absolute bottom-2 right-2 text-xs bg-black bg-opacity-70 px-2 py-1 rounded-full text-emerald-400 font-medium">
                      可灵AI
                    </div>
                  </div>
                ))
              }
              {item.images && item.images.length > 4 && (
                <div className="absolute bottom-2 right-2 text-xs bg-black/75 text-white px-2 py-1 rounded-full z-10">
                  +{item.images.length - 4}
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="p-4 flex items-center justify-between border-t border-gray-800 bg-gray-900/80">
              <div className="flex items-center space-x-6">
                <button className="flex items-center text-gray-400 hover:text-rose-500 transition-colors">
                  <Heart className="h-5 w-5 mr-1" />
                  <span className="text-sm">{10 + (index % 40)}</span>
                </button>
                <button className="flex items-center text-gray-400 hover:text-cyan-500 transition-colors" disabled={item.isLoading}>
                  <MessageSquare className="h-5 w-5 mr-1" />
                  <span className="text-sm">{2 + (index % 8)}</span>
                </button>
                <button className="flex items-center text-gray-400 hover:text-emerald-500 transition-colors">
                  <Share className="h-5 w-5" />
                </button>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="text-xs bg-gray-800 border-gray-700 text-emerald-300 hover:bg-gray-700 hover:text-emerald-200"
                disabled={item.isLoading}
              >
                {item.isLoading ? '生成中...' : '查看更多'}
              </Button>
            </div>
          </Card>
        ))}

        {/* Show fetched data from API if not using generation history exclusively */}
        {!disableAutoFetch && data.map((item, index) => (
          <Card
            key={item.id}
            className={`overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-shadow bg-gray-900 border-gray-800 ${
              index % 5 === 0 ? 'border-2 border-emerald-500' : ''
            }`}
          >
            {/* Author Header */}
            <div className="p-4 flex items-center justify-between border-b border-gray-800 bg-gray-900">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-gray-700 overflow-hidden mr-3 border-2 border-emerald-500/30">
                  <div className="w-full h-full flex items-center justify-center text-emerald-500 font-bold">
                    {item.title ? item.title.charAt(0).toUpperCase() : "H"}
                  </div>
                </div>
                <div>
                  <div className="font-semibold text-white">{item.title || "History Image"}</div>
                  <div className="text-sm text-emerald-300">@user{item.user_id.substring(0, 6)}</div>
                </div>
              </div>
              <div className="text-xs text-gray-400 bg-gray-800 px-2 py-1 rounded-full">
                {new Date(item.created_at).toLocaleDateString()}
              </div>
            </div>

            {/* Prompt */}
            <div className="px-4 py-3 border-b border-gray-800 bg-gray-900/70">
              <p className="text-sm text-gray-300">{item.prompt || "No prompt available"}</p>
            </div>

            {/* Images Grid */}
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 p-2 bg-gray-950">
              {item.image_url && (
                <div className="relative aspect-[3/4] group overflow-hidden rounded-lg">
                  <img
                    src={parseImageUrl(item.image_url)}
                    alt={item.title || "Generated image"}
                    className="w-full h-full object-cover rounded-lg"
                  />
                  <div className="absolute bottom-2 right-2 text-xs bg-black bg-opacity-70 px-2 py-1 rounded-full text-emerald-400 font-medium">
                    可灵AI
                  </div>
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="p-4 flex items-center justify-between border-t border-gray-800 bg-gray-900/80">
              <div className="flex items-center space-x-6">
                <button className="flex items-center text-gray-400 hover:text-rose-500 transition-colors">
                  <Heart className="h-5 w-5 mr-1" />
                  <span className="text-sm">{10 + (index % 40)}</span>
                </button>
                <button className="flex items-center text-gray-400 hover:text-cyan-500 transition-colors">
                  <MessageSquare className="h-5 w-5 mr-1" />
                  <span className="text-sm">{2 + (index % 8)}</span>
                </button>
                <button className="flex items-center text-gray-400 hover:text-emerald-500 transition-colors">
                  <Share className="h-5 w-5" />
                </button>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="text-xs bg-gray-800 border-gray-700 text-emerald-300 hover:bg-gray-700 hover:text-emerald-200"
              >
                查看更多
              </Button>
            </div>
          </Card>
        ))}
      </div>
    );
  }

  // Default view - showing fetched API data
  return (
    <div className="space-y-6">
      {data.map((item, index) => (
        <Card
          key={item.id}
          className={`overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-shadow bg-gray-900 border-gray-800 ${
            index % 5 === 0 ? 'border-2 border-emerald-500' : ''
          }`}
        >
          {/* Author Header */}
          <div className="p-4 flex items-center justify-between border-b border-gray-800 bg-gray-900">
            <div className="flex items-center">
              <div className="w-10 h-10 rounded-full bg-gray-700 overflow-hidden mr-3 border-2 border-emerald-500/30">
                <div className="w-full h-full flex items-center justify-center text-emerald-500 font-bold">
                  {item.title ? item.title.charAt(0).toUpperCase() : "A"}
                </div>
              </div>
              <div>
                <div className="font-semibold text-white">{item.title || "Untitled Image"}</div>
                <div className="text-sm text-emerald-300">@user{item.user_id.substring(0, 6)}</div>
              </div>
            </div>
            <div className="text-xs text-gray-400 bg-gray-800 px-2 py-1 rounded-full">
              {new Date(item.created_at).toLocaleDateString()}
            </div>
          </div>

          {/* Prompt */}
          <div className="px-4 py-3 border-b border-gray-800 bg-gray-900/70">
            <p className="text-sm text-gray-300">{item.prompt || "No prompt available"}</p>
          </div>

          {/* Images Grid */}
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 p-2 bg-gray-950">
            {item.image_url && (
              <div className="relative aspect-[3/4] group overflow-hidden rounded-lg">
                <img
                  src={parseImageUrl(item.image_url)}
                  alt={item.title || "Generated image"}
                  className="w-full h-full object-cover rounded-lg"
                />
                <div className="absolute bottom-2 right-2 text-xs bg-black bg-opacity-70 px-2 py-1 rounded-full text-emerald-400 font-medium">
                  可灵AI
                </div>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="p-4 flex items-center justify-between border-t border-gray-800 bg-gray-900/80">
            <div className="flex items-center space-x-6">
              <button className="flex items-center text-gray-400 hover:text-rose-500 transition-colors">
                <Heart className="h-5 w-5 mr-1" />
                <span className="text-sm">{10 + (index % 40)}</span>
              </button>
              <button className="flex items-center text-gray-400 hover:text-cyan-500 transition-colors">
                <MessageSquare className="h-5 w-5 mr-1" />
                <span className="text-sm">{2 + (index % 8)}</span>
              </button>
              <button className="flex items-center text-gray-400 hover:text-emerald-500 transition-colors">
                <Share className="h-5 w-5" />
              </button>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="text-xs bg-gray-800 border-gray-700 text-emerald-300 hover:bg-gray-700 hover:text-emerald-200"
            >
              查看更多
            </Button>
          </div>
        </Card>
      ))}
    </div>
  );
}
