import { getSupabaseClient } from "./db";
// import { db } from "@/lib/db"
import { createClient } from '@supabase/supabase-js'

// 使用环境变量或提供默认值防止构建失败
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL || '';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || process.env.SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseKey);

export interface Image {
  id: string;
  category_id: string;
  prompt: string;
  image_url: string;
  title?: string;
  description?: string;
  user_id?: string;
  status?: string;
  metadata?: any;
  created_at?: string;
  updated_at?: string;
}

export async function createImage(imageData: Omit<Image, 'id' | 'created_at' | 'updated_at'>) {
  const supabase = getSupabaseClient();
  
  const { data, error } = await supabase
    .from('images')
    .insert(imageData)
    .select()
    .single();
  
  if (error) {
    console.error("Error creating image:", error);
    throw error;
  }
  
  return data;
}

export async function getImageById(imageId: string) {
  // 处理空或undefined的imageId
  if (!imageId || imageId === 'undefined') {
    console.log(`Invalid image ID provided: ${imageId}`);
    return null;
  }
  
  console.log(`Getting image with ID: ${imageId}`);
  const supabase = getSupabaseClient();
  
  try {
    // 首先尝试使用特定的外键名称
    const { data, error } = await supabase
      .from('images')
      .select('*, categories!images_category_id_fkey(name, slug)')
      .eq('id', imageId)
      .single();
    
    if (error) {
      console.error("Error with specific foreign key:", error);
      
      // 尝试不使用关联查询，仅获取图片数据
      const { data: imageOnly, error: imageError } = await supabase
        .from('images')
        .select('*')
        .eq('id', imageId)
        .single();
      
      if (imageError) {
        console.error("Error fetching image only:", imageError);
        throw new Error(`无法找到图片: ${imageError.message}`);
      }
      
      console.log(`Found image: ${JSON.stringify(imageOnly)}`);
      
      // 如果有类别ID，单独获取类别信息
      if (imageOnly.category_id) {
        const { data: categoryData, error: categoryError } = await supabase
          .from('categories')
          .select('name, slug')
          .eq('id', imageOnly.category_id)
          .single();
        
        if (categoryError) {
          console.error("Error fetching category:", categoryError);
        } else if (categoryData) {
          // 手动添加类别信息
          imageOnly.categories = categoryData;
        }
      }
      
      return imageOnly;
    }
    
    console.log(`Found image with relation: ${JSON.stringify(data)}`);
    return data;
  } catch (err) {
    console.error("Unexpected error in getImageById:", err);
    throw err;
  }
}

export async function getImagesByCategory(categoryId: string, limit = 20, offset = 0) {
  const supabase = getSupabaseClient();
  
  const { data, error } = await supabase
    .from('images')
    .select('*')
    .eq('category_id', categoryId)
    .eq('status', 'active')
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);
  
  if (error) {
    console.error("Error fetching images by category:", error);
    throw error;
  }
  
  return data;
}

export async function getImagesByUser(userId: string, limit = 20, offset = 0) {
  const supabase = getSupabaseClient();
  
  const { data, error } = await supabase
    .from('images')
    .select('*, categories!images_category_id_fkey(name, slug)')
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);
  
  if (error) {
    console.error("Error fetching images by user:", error);
    throw error;
  }
  
  return data;
}

export async function getImageHistory(limit: number = 10, offset: number = 0): Promise<Image[]> {
  try {
    const { data, error } = await supabase
      .from('images')
      .select('*')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error("Error fetching image history:", error)
      return []
    }

    return data || []
  } catch (error) {
    console.error("Error fetching image history:", error)
    return []
  }
} 