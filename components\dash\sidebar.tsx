import {
    LayoutDashboard,
    MessageSquare,
    Search,
    Sparkles,
    Palette,
    Target,
    LayoutGrid,
    FileText,
    Users,
    ImageIcon,
    Settings,
    RotateCcw,
    ChevronLeft,
    ChevronRight,
    Menu,
  } from "lucide-react"
  import Link from "next/link"
  import { useState, useEffect } from "react"
  
  interface SidebarProps {
    collapsed?: boolean;
    onToggle?: (collapsed: boolean) => void;
  }
  
  export function Sidebar({ collapsed: propCollapsed, onToggle }: SidebarProps) {
    const [collapsed, setCollapsed] = useState(propCollapsed || false);
    
    useEffect(() => {
      if (propCollapsed !== undefined && propCollapsed !== collapsed) {
        setCollapsed(propCollapsed);
      }
    }, [propCollapsed]);
    
    const handleToggle = () => {
      const newCollapsedState = !collapsed;
      setCollapsed(newCollapsedState);
      if (onToggle) {
        onToggle(newCollapsedState);
      }
    };
    
    return (
      <div className={`${collapsed ? 'w-20' : 'w-60'} h-full bg-gray-800 text-white border-r border-gray-700 flex flex-col overflow-hidden transition-all duration-300`}>
        <div className={`p-4 flex items-center ${collapsed ? 'justify-center' : 'justify-between'}`}>
          <div className="flex items-center">
            <div className="bg-[#6941C6] text-white p-1 rounded">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z"
                  fill="white"
                />
                <path
                  d="M18 8.5C19.1046 8.5 20 7.60457 20 6.5C20 5.39543 19.1046 4.5 18 4.5C16.8954 4.5 16 5.39543 16 6.5C16 7.60457 16.8954 8.5 18 8.5Z"
                  fill="white"
                />
                <path
                  d="M6 8.5C7.10457 8.5 8 7.60457 8 6.5C8 5.39543 7.10457 4.5 6 4.5C4.89543 4.5 4 5.39543 4 6.5C4 7.60457 4.89543 8.5 6 8.5Z"
                  fill="white"
                />
                <path
                  d="M18 19.5C19.1046 19.5 20 18.6046 20 17.5C20 16.3954 19.1046 15.5 18 15.5C16.8954 15.5 16 16.3954 16 17.5C16 18.6046 16.8954 19.5 18 19.5Z"
                  fill="white"
                />
                <path
                  d="M6 19.5C7.10457 19.5 8 18.6046 8 17.5C8 16.3954 7.10457 15.5 6 15.5C4.89543 15.5 4 16.3954 4 17.5C4 18.6046 4.89543 19.5 6 19.5Z"
                  fill="white"
                />
              </svg>
            </div>
            {!collapsed && <span className="ml-2 font-bold text-xl">SOCIALDUDE</span>}
          </div>
          
          {/* Toggle button now in the header - only visible on desktop */}
          <button 
            onClick={handleToggle}
            className="hidden md:flex items-center justify-center p-1 hover:bg-gray-700 rounded transition-colors"
            aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
          >
            {collapsed ? <ChevronRight size={16} /> : <ChevronLeft size={16} />}
          </button>
        </div>
  
        <div className="flex flex-col flex-1 overflow-y-auto">
          <div className="px-3 py-2">
            <Link href="/" className={`flex items-center ${collapsed ? 'justify-center' : ''} px-3 py-2 text-sm font-medium rounded-md bg-gray-700`}>
              <LayoutDashboard className={`${collapsed ? '' : 'mr-3'} h-5 w-5 text-gray-300`} />
              {!collapsed && (
                <>
                  <span>Dashboard</span>
                  <span className="ml-auto bg-purple-900 text-purple-200 text-xs font-semibold px-2 py-0.5 rounded">35</span>
                </>
              )}
            </Link>
          </div>
  
          {/* Featured Section */}
          <div className="px-3 py-2">
            {!collapsed ? (
              <h3 className="px-3 text-xs font-semibold text-gray-400 uppercase tracking-wider">Featured</h3>
            ) : (
              <div className="h-5 flex justify-center">
                <div className="w-8 border-t border-gray-700"></div>
              </div>
            )}
            <div className={`mt-2 space-y-1 ${collapsed ? 'flex flex-col items-center space-y-4 pt-2' : ''}`}>
              <Link
                href="/"
                className={`flex items-center ${collapsed ? 'justify-center w-10 h-10 rounded-full hover:bg-gray-700' : 'px-3 py-2 rounded-md text-gray-200 hover:bg-gray-700'}`}
              >
                <MessageSquare className={`${collapsed ? '' : 'mr-3'} h-5 w-5 text-gray-400`} />
                {!collapsed && <span>AI Image Generator</span>}
              </Link>
              <Link
                href="/"
                className={`flex items-center ${collapsed ? 'justify-center w-10 h-10 rounded-full hover:bg-gray-700' : 'px-3 py-2 rounded-md text-gray-200 hover:bg-gray-700'}`}
              >
                <Search className={`${collapsed ? '' : 'mr-3'} h-5 w-5 text-gray-400`} />
                {!collapsed && <span>AI Anime Generator</span>}
              </Link>
              <Link
                href="/"
                className={`flex items-center ${collapsed ? 'justify-center w-10 h-10 rounded-full hover:bg-gray-700' : 'px-3 py-2 rounded-md text-gray-200 hover:bg-gray-700'}`}
              >
                <Sparkles className={`${collapsed ? '' : 'mr-3'} h-5 w-5 text-gray-400`} />
                {!collapsed && <span>AI Logo Generator  </span>}
              </Link>
            </div>
          </div>
  
          {/* Categories Section */}
          <div className="px-3 py-2">
            {!collapsed ? (
              <h3 className="px-3 text-xs font-semibold text-gray-400 uppercase tracking-wider">Categories</h3>
            ) : (
              <div className="h-5 flex justify-center">
                <div className="w-8 border-t border-gray-700"></div>
              </div>
            )}
            <div className={`mt-2 space-y-1 ${collapsed ? 'flex flex-col items-center space-y-4 pt-2' : ''}`}>
              <Link
                href="/"
                className={`flex items-center ${collapsed ? 'justify-center w-10 h-10 rounded-full hover:bg-gray-700' : 'px-3 py-2 rounded-md text-gray-200 hover:bg-gray-700'}`}
              >
                <Palette className={`${collapsed ? '' : 'mr-3'} h-5 w-5 text-gray-400`} />
                {!collapsed && <span>HOME</span>}
              </Link>
              <Link
                href="/"
                className={`flex items-center ${collapsed ? 'justify-center w-10 h-10 rounded-full hover:bg-gray-700' : 'px-3 py-2 rounded-md text-gray-200 hover:bg-gray-700'}`}
              >
                <Target className={`${collapsed ? '' : 'mr-3'} h-5 w-5 text-gray-400`} />
                {!collapsed && <span>HISTORY</span>}
              </Link>
              <Link
                href="/"
                className={`flex items-center ${collapsed ? 'justify-center w-10 h-10 rounded-full hover:bg-gray-700' : 'px-3 py-2 rounded-md text-gray-200 hover:bg-gray-700'}`}
              >
                <LayoutGrid className={`${collapsed ? '' : 'mr-3'} h-5 w-5 text-gray-400`} />
                {!collapsed && <span>DISCORD</span>}
              </Link>
              <Link
                href="/"
                className={`flex items-center ${collapsed ? 'justify-center w-10 h-10 rounded-full hover:bg-gray-700' : 'px-3 py-2 rounded-md text-gray-200 hover:bg-gray-700'}`}
              >
                <FileText className={`${collapsed ? '' : 'mr-3'} h-5 w-5 text-gray-400`} />
                {!collapsed && <span>CONTENT </span>}
              </Link>
              <Link
                href="/"
                className={`flex items-center ${collapsed ? 'justify-center w-10 h-10 rounded-full hover:bg-gray-700' : 'px-3 py-2 rounded-md text-gray-200 hover:bg-gray-700'}`}
              >
                <Users className={`${collapsed ? '' : 'mr-3'} h-5 w-5 text-gray-400`} />
                {!collapsed && <span>AUDIENCE </span>}
              </Link>
              <Link
                href="/"
                className={`flex items-center ${collapsed ? 'justify-center w-10 h-10 rounded-full hover:bg-gray-700' : 'px-3 py-2 rounded-md text-gray-200 hover:bg-gray-700'}`}
              >
                <ImageIcon className={`${collapsed ? '' : 'mr-3'} h-5 w-5 text-gray-400`} />
                {!collapsed && <span>MEDIA </span>}
              </Link>
            </div>
          </div>
  
          {/* Settings Section */}
          {/* <div className="px-3 py-2">
            {!collapsed ? (
              <h3 className="px-3 text-xs font-semibold text-gray-400 uppercase tracking-wider">Settings</h3>
            ) : (
              <div className="h-5 flex justify-center">
                <div className="w-8 border-t border-gray-700"></div>
              </div>
            )}
            <div className={`mt-2 space-y-1 ${collapsed ? 'flex flex-col items-center space-y-4 pt-2' : ''}`}>
              <Link
                href="/"
                className={`flex items-center ${collapsed ? 'justify-center w-10 h-10 rounded-full hover:bg-gray-700' : 'px-3 py-2 rounded-md text-gray-200 hover:bg-gray-700'}`}
              >
                <Settings className={`${collapsed ? '' : 'mr-3'} h-5 w-5 text-gray-400`} />
                {!collapsed && (
                  <>
                    <span>Settings</span>
                    <svg className="ml-auto h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                      <path
                        fillRule="evenodd"
                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </>
                )}
              </Link>
            </div>
          </div> */}
  
          {/* SocialDude V1 Section */}
          {/* <div className="px-3 py-2">
            {!collapsed ? (
              <h3 className="px-3 text-xs font-semibold text-gray-400 uppercase tracking-wider">HISTORY</h3>
            ) : (
              <div className="h-5 flex justify-center">
                <div className="w-8 border-t border-gray-700"></div>
              </div>
            )}
            <div className={`mt-2 space-y-1 ${collapsed ? 'flex flex-col items-center space-y-4 pt-2' : ''}`}>
              <Link
                href="/"
                className={`flex items-center ${collapsed ? 'justify-center w-10 h-10 rounded-full hover:bg-gray-700' : 'px-3 py-2 rounded-md text-gray-200 hover:bg-gray-700'}`}
              >
                <RotateCcw className={`${collapsed ? '' : 'mr-3'} h-5 w-5 text-gray-400`} />
                {!collapsed && <span>MY HISTORY</span>}
              </Link>
            </div>
          </div> */}
  
          {collapsed ? (
            <div className="mt-auto p-4 flex justify-center">
              <div className="bg-purple-900 p-2 rounded-full">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="12" cy="12" r="10" stroke="#6941C6" strokeWidth="2" />
                  <path d="M12 8V16" stroke="#6941C6" strokeWidth="2" strokeLinecap="round" />
                  <path d="M8 12H16" stroke="#6941C6" strokeWidth="2" strokeLinecap="round" />
                </svg>
              </div>
            </div>
          ) : (
            <div className="mt-auto p-4">
              <div className="bg-gray-700 p-3 rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-300">1 / 10 Free Generations</span>
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12" r="10" stroke="#6941C6" strokeWidth="2" />
                    <path d="M12 8V16" stroke="#6941C6" strokeWidth="2" strokeLinecap="round" />
                    <path d="M8 12H16" stroke="#6941C6" strokeWidth="2" strokeLinecap="round" />
                  </svg>
                </div>
                <div className="mt-2 h-1 w-full bg-gray-600 rounded-full overflow-hidden">
                  <div className="h-full bg-purple-600 rounded-full" style={{ width: "10%" }}></div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    )
  }
  