import { Metadata } from "next"
import { getTranslations } from "next-intl/server"
import PromptLibrary from "@/components/keling/PromptLibrary"

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("pages.landing")
  return {
    title: "AI 提示词生成器 - AI图片生成",
    description: "快速生成和优化AI图片提示词",
  }
}

export default async function PromptLibraryPage() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-[1600px] mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-white mb-4">AI 提示词生成器</h1>
            <p className="text-gray-400 text-lg">输入关键词，快速生成优化的AI图片提示词</p>
          </div>
          <PromptLibrary />
        </div>
      </div>
    </div>
  )
}
