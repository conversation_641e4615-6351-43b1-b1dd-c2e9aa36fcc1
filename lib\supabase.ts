import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Create a single supabase client for interacting with your database
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

export async function signInWithEmail(email: string, password: string) {
  console.log('正在尝试使用邮箱登录:', email);
  const result = await supabase.auth.signInWithPassword({ email, password });
  console.log('Supabase登录结果:', JSON.stringify(result, null, 2));
  return result;
}

export async function signUpWithEmail(email: string, password: string) {
  console.log('正在尝试注册新用户:', email);
  const result = await supabase.auth.signUp({ 
    email, 
    password,
    options: {
      emailRedirectTo: `${window.location.origin}/loginseo`,
    }
  });
  console.log('Supabase注册结果:', JSON.stringify(result, null, 2));
  return result;
}

export async function resetPassword(email: string) {
  return supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${window.location.origin}/auth/reset-password`,
  });
}