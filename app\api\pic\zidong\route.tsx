import { NextResponse } from "next/server";
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { auth } from "@/auth";
import { getUserCredits, decreaseCredits, CreditsTransType } from "@/services/credit";
import { createImage } from "@/models/image";
import { getSupabaseClient } from "@/models/db";
import { withApiLogger } from "@/lib/api-logger";
import { NextRequest } from "next/server";

// 对于Cloudflare部署，必须使用Edge Runtime
export const runtime = "edge";

// Initialize R2 client
const s3 = new S3Client({
  region: "auto",
  endpoint: `https://${process.env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.CLOUDFLARE_SECRET_ACCESS_KEY || '',
  },
});

// Credits cost per image
const CREDITS_PER_IMAGE = 3;

// 原始处理函数，不修改逻辑
async function handleGenerateRequest(req: Request) {
  try {
    // Authenticate user
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 });
    }

    const userUuid = session.user.uuid;
    
    // Parse request parameters
    const { 
      prompt, 
      width = 768, 
      height = 1152, 
      count = 1, 
      seed: baseSeed, 
      model = "flux.schnell", 
      categoryId, 
      style = "" 
    } = await req.json();
    
    // Validate parameters
    if (!prompt) {
      return NextResponse.json({ error: "Prompt is required" }, { status: 400 });
    }

    if (!categoryId) {
      return NextResponse.json({ error: "Category ID is required" }, { status: 400 });
    }
    
    // Limit max image count to 4
    const imageCount = Math.min(Math.max(1, parseInt(String(count)) || 1), 4);
    
    // Build final prompt
    const aiPrompt = style ? `${prompt}. Style: ${style}` : prompt;
    
    // Generate base seed if not provided
    const initialSeed = baseSeed || Math.floor(Math.random() * 1000000);
    
    // Generate and store images
    const generatedUrls: string[] = [];
    const originalUrls: string[] = [];
    
    for (let i = 0; i < imageCount; i++) {
      try {
        const currentSeed = initialSeed + i;
        
        // Build API URL for image generation
        const encodedPrompt = encodeURIComponent(aiPrompt);
        const url = `https://image.pollinations.ai/prompt/${encodedPrompt}`;
        
        const params = new URLSearchParams({
          width: width.toString(),
          height: height.toString(),
          seed: currentSeed.toString(),
          model,
          private: 'true'
        });
        
        const pollinationsUrl = `${url}?${params.toString()}`;
        
        // Fetch image
        const response = await fetch(pollinationsUrl);
        if (!response.ok) {
          throw new Error(`Failed to generate image ${i+1}/${imageCount}`);
        }
        
        const imageBuffer = await response.arrayBuffer();
        
        // Upload to R2
        const fileName = `ai-image-${Date.now()}-${currentSeed}-${i}.png`;
        
        await s3.send(
          new PutObjectCommand({
            Bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
            Key: fileName,
            Body: Buffer.from(imageBuffer),
            ContentType: 'image/png',
          })
        );
        
        const r2Url = `${process.env.NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_DOMAIN}/${fileName}`;
        
        // Store URLs
        generatedUrls.push(r2Url);
        originalUrls.push(pollinationsUrl);
        
        // Save image to database
        await createImage({
          category_id: categoryId,
          prompt,
          image_url: r2Url,
          user_id: userUuid,
          status: 'active',
          metadata: {
            width,
            height,
            seed: currentSeed,
            model,
            style,
            aiPrompt,
            originalUrl: pollinationsUrl,
            imageIndex: i,
            totalImages: imageCount
          }
        });
      } catch (error) {
        console.error(`Error generating image ${i+1}/${imageCount}:`, error);
        if (generatedUrls.length > 0) break;
        throw error;
      }
    }
    
    // Save collection to database
    if (generatedUrls.length > 0) {
      const supabase = getSupabaseClient();
      await supabase
        .from('imagesing')
        .insert({
          category_id: categoryId,
          prompt: prompt,
          image_url: JSON.stringify(generatedUrls),
          user_id: userUuid,
          status: 'active',
          metadata: { 
            style, 
            sourcePrompt: prompt, 
            aiPrompt,
            model,
            width,
            height,
            seed: initialSeed,
            imageCount: generatedUrls.length,
            allImages: generatedUrls, 
            allOriginalUrls: originalUrls,
            images: generatedUrls
          }
        });
    }
    
    // Return results
    return NextResponse.json({
      success: true,
      images: generatedUrls,
      originalUrls,
    });
    
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Image generation failed" },
      { status: 500 }
    );
  }
}

// 导出的POST方法，使用withApiLogger包装原始处理函数
export async function POST(req: NextRequest) {
  return withApiLogger(async (req) => {
    return handleGenerateRequest(req as unknown as Request);
  })(req);
} 