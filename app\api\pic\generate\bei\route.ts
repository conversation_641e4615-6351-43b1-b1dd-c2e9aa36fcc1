import { NextResponse } from "next/server";
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { auth } from "@/auth";
import { getUserCredits, decreaseCredits, CreditsTransType } from "@/services/credit";
import { createImage } from "@/models/image";

// 对于Cloudflare部署，必须使用Edge Runtime
export const runtime = "edge";

// 初始化 R2 客户端
const s3 = new S3Client({
  region: "auto",
  endpoint: `https://${process.env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.CLOUDFLARE_SECRET_ACCESS_KEY || '',
  },
});

// 每次生成消耗的积分
const CREDITS_PER_GENERATION = 3;

export async function POST(req: Request) {
  try {
    // Check if user is authenticated
    const session = await auth();
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "您需要登录才能生成图片" },
        { status: 401 }
      );
    }

    const userUuid = session.user.uuid;
    
    // 检查用户积分是否足够
    const userCredits = await getUserCredits(userUuid);
    if (userCredits.left_credits < CREDITS_PER_GENERATION) {
      return NextResponse.json(
        { error: `积分不足，生成图片需要 ${CREDITS_PER_GENERATION} 积分，您当前有 ${userCredits.left_credits} 积分` },
        { status: 402 }
      );
    }

    const { prompt, width = 1024, height = 1024, seed = 42, model, categoryId } = await req.json();

    if (!prompt) {
      return NextResponse.json(
        { error: "请输入提示词" },
        { status: 400 }
      );
    }

    if (!categoryId) {
      return NextResponse.json(
        { error: "请选择分类" },
        { status: 400 }
      );
    }
    
    console.log(`Generating image with category ID: ${categoryId}`);

    // 构建Pollinations API URL
    const encodedPrompt = encodeURIComponent(prompt);
    let url = `https://image.pollinations.ai/prompt/${encodedPrompt}`;
    
    // 添加参数
    const params = new URLSearchParams();
    params.append('width', width.toString());
    params.append('height', height.toString());
    params.append('seed', seed.toString());
    if (model) params.append('model', model);
    params.append('private', 'true');
    
    const pollinationsUrl = `${url}?${params.toString()}`;
    console.log('Pollinations URL:', pollinationsUrl);
    
    // 获取图片
    const response = await fetch(pollinationsUrl);
    if (!response.ok) {
      throw new Error('获取图片失败，请检查提示词或稍后再试');
    }
    
    const imageBuffer = await response.arrayBuffer();
    
    // 上传到 R2
    const fileName = `ai-image-${Date.now()}-${seed}.png`;
    
    await s3.send(
      new PutObjectCommand({
        Bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
        Key: fileName,
        Body: Buffer.from(imageBuffer),
        ContentType: 'image/png',
      })
    );
    
    const r2Url = `${process.env.NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_DOMAIN}/${fileName}`;
    console.log('R2 URL:', r2Url);

    // 扣除积分
    await decreaseCredits({
      user_uuid: userUuid,
      trans_type: CreditsTransType.SystemAdd,
      credits: CREDITS_PER_GENERATION
    });

    // 保存图片信息到数据库
    const imageData = {
      category_id: categoryId,
      prompt,
      image_url: r2Url,
      user_id: userUuid,
      status: 'active',
      metadata: {
        width,
        height,
        seed,
        model: model || 'default',
        originalUrl: pollinationsUrl
      }
    };

    console.log('Saving image with data:', JSON.stringify({
      ...imageData,
      image_url: '[truncated]' // 避免日志过长
    }, null, 2));

    const savedImage = await createImage(imageData);
    console.log('Image saved successfully with ID:', savedImage.id);

    return NextResponse.json({ 
      success: true,
      imageId: savedImage.id,
      imageUrl: r2Url,
      originalUrl: pollinationsUrl,
      prompt,
      width,
      height,
      seed,
      model: model || 'default',
      creditsUsed: CREDITS_PER_GENERATION,
      creditsRemaining: userCredits.left_credits - CREDITS_PER_GENERATION
    });

  } catch (error) {
    console.error('API 错误:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "图片生成失败" },
      { status: 500 }
    );
  }
} 