import { NextResponse } from "next/server";
import { auth } from "@/auth";

export async function GET() {
  try {
    console.log('Testing authentication...');
    
    const session = await auth();
    console.log('Session data:', JSON.stringify(session, null, 2));
    
    return NextResponse.json({
      success: true,
      session: session,
      hasUser: !!session?.user,
      userId: session?.user?.id,
      userUuid: session?.user?.uuid,
      userEmail: session?.user?.email
    });
    
  } catch (error) {
    console.error('Auth test error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
