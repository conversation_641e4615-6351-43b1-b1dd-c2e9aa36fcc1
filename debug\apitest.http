@baseUrl = http://localhost:3000
@apiKey = sk-xxx

### ping api
POST {{baseUrl}}/api/ping
Content-Type: application/json
Authorization: Bearer {{apiKey}}

{
  "message": "hello"
}

### gen image
POST {{baseUrl}}/api/demo/gen-image
Content-Type: application/json
Authorization: Bearer {{apiKey}}

{
  "prompt": "a beautiful girl running with 2 cats",
  "provider": "replicate",
  "model": "black-forest-labs/flux-schnell"
}

### gen text 
POST {{baseUrl}}/api/demo/gen-text
Content-Type: application/json
Authorization: Bearer {{apiKey}}

{
  "prompt": "9.11 vs 9.8, which one is greater?",
  "provider": "openrouter",
  "model": "deepseek/deepseek-r1"
}

### test openrouter chat
POST https://www.xinfengzc.com/api/openrouter/chat
Content-Type: application/json

{
  "model": "google/gemini-2.5-flash-preview-05-20",
  "messages": [
    {
      "role": "user",
      "content": "你打印出来的字太少了"
    }
  ],
  "stream": false,
  "max_tokens": 1000
}