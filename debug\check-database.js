// 检查数据库中的 prompts 状态
// 运行: node debug/check-database.js

const fetch = require('node-fetch');

async function checkDatabase() {
  console.log('🔍 检查数据库中的 prompts 状态...\n');

  try {
    // 创建一个简单的 API 端点来查询数据库
    const response = await fetch('http://localhost:3000/api/test-auth');
    
    if (!response.ok) {
      console.log('❌ 无法连接到服务器');
      return;
    }

    console.log('✅ 服务器连接正常');
    console.log('\n💡 请在 Supabase 控制台中运行以下 SQL 查询来检查数据库状态:');
    console.log('');
    console.log('-- 查看最近的 prompts 记录');
    console.log('SELECT id, keyword, status, prediction_id, image_url, created_at, updated_at');
    console.log('FROM prompts');
    console.log('ORDER BY created_at DESC');
    console.log('LIMIT 10;');
    console.log('');
    console.log('-- 查看处理中的记录');
    console.log('SELECT id, keyword, status, prediction_id, created_at');
    console.log('FROM prompts');
    console.log('WHERE status = \'processing\'');
    console.log('ORDER BY created_at DESC;');
    console.log('');
    console.log('-- 查看已完成的记录');
    console.log('SELECT id, keyword, status, image_url, created_at');
    console.log('FROM prompts');
    console.log('WHERE status = \'completed\'');
    console.log('ORDER BY created_at DESC');
    console.log('LIMIT 5;');
    console.log('');
    console.log('🔗 Supabase 控制台: https://supabase.com/dashboard/project/YOUR_PROJECT_ID/editor');

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  }
}

// 运行检查
checkDatabase();
