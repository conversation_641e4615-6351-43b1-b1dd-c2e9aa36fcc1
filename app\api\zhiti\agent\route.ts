import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
export const runtime = "edge";

export async function GET(req: NextRequest) {
  try {
    // 从URL获取智能体ID
    const { searchParams } = new URL(req.url);
    const id = searchParams.get("id");
    
    if (!id) {
      return NextResponse.json(
        { error: "未提供智能体ID" },
        { status: 400 }
      );
    }
    
    // 从数据库获取智能体信息
    const { data, error } = await supabase
      .from("agents")
      .select("*")
      .eq("id", id)
      .single();
    
    if (error) {
      console.error("获取智能体信息失败:", error);
      return NextResponse.json(
        { error: "获取智能体信息失败" },
        { status: 500 }
      );
    }
    
    if (!data) {
      return NextResponse.json(
        { error: "找不到指定的智能体" },
        { status: 404 }
      );
    }
    
    // 返回智能体信息
    return NextResponse.json(data);
    
  } catch (error) {
    console.error("处理请求时出错:", error);
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    );
  }
} 