import { Metadata } from "next"
import PromptHistory from "@/components/keling/PromptHistory"
import DashboardClient from "@/components/dash/DashboardClient"

export const metadata: Metadata = {
  title: "历史记录",
  description: "查看您的提示词历史记录",
}

export default function HistoryPage() {
  return (
    <DashboardClient>
      <div className="flex-1 flex flex-col h-[calc(100vh-4rem)]">
      {/* <div className="flex-none p-8 pb-4">
          <h1 className="text-3xl font-bold tracking-tight text-white">历史记录</h1>
          <p className="text-gray-400 mt-2">
            查看和管理您的提示词历史记录
          </p>
        </div> */}
        <div className="flex-1 px-8 pb-8 overflow-hidden">
          <div className="h-full">
            <PromptHistory />
          </div>
        </div>
      </div>
    </DashboardClient>
  )
}
