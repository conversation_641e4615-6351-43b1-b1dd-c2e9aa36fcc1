"use client"

import { useState, useEffect } from "react"
import { Loader2, Refresh<PERSON><PERSON>, Upload, Download, Info, Sparkles, Heart, MessageSquare, Share, Copy, Check, X, ChevronDown, Zap } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardHeader, CardContent, CardFooter, CardTitle } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { toast } from "sonner"
import React, { useRef } from "react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"

// Import next-auth hooks for authentication
import { useSession, signIn } from "next-auth/react"

// Add a dialog for insufficient credits
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Dialog<PERSON>rigger,
} from "@/components/ui/dialog"

import ModelSelection from "../modou/ModelSelection";
// import Results from "../modou/Results";
import Results from "../modou/pubu";
// import { ScrollArea } from "@/components/ui/scroll-area"
import Image from "next/image"

// Add this section after imports to define animation styles
import { cn } from "@/lib/utils"

// Define animation classes
const shimmerAnimation = "before:absolute before:inset-0 before:-translate-x-full before:animate-[shimmer_2s_infinite] before:bg-gradient-to-r before:from-transparent before:via-white/10 before:to-transparent"

interface ImageGalleryProps {
  onGenerate?: (images: string[]) => void
}

const GenerationPlaceholder = () => {
  return (
    <div className="relative group overflow-hidden rounded-lg shadow-lg bg-gray-900/70 border-gray-700/50">
      <div className={cn("aspect-[3/4] bg-gray-900 overflow-hidden flex flex-col items-center justify-center relative", shimmerAnimation)}>
        <Loader2 className="h-10 w-10 text-emerald-400 animate-spin mb-3" />
        <p className="text-gray-400 text-sm animate-pulse">Generating image...</p>
      </div>
      <div className="absolute top-2 right-2 text-xs bg-black bg-opacity-70 px-2 py-1 rounded-full text-emerald-400 font-medium">
        生成中
      </div>
    </div>
  );
};

const Zidong = ({ onGenerate }: ImageGalleryProps) => {
  const [selectedAspectRatio, setSelectedAspectRatio] = useState("2:3")
  const [generationCount, setGenerationCount] = useState(4)
  const [prompt, setPrompt] = useState("")
  const [isOptimizing, setIsOptimizing] = useState(false)
  const [optimizedPrompts, setOptimizedPrompts] = useState<string[]>([])
  const [selectedOptimizedPrompt, setSelectedOptimizedPrompt] = useState<string>("")
  const [selectedImage, setSelectedImage] = useState<File | null>(null)
  const [generatedImages, setGeneratedImages] = useState<string[]>([])
  const [isGenerating, setIsGenerating] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<string>("")
  const [selectedModel, setSelectedModel] = useState("flux.schnell")
  
  // Add state for user credits
  const [userCredits, setUserCredits] = useState<number>(0)
  // Add state for the cost per generation
  const [creditCost, setCreditCost] = useState<number>(3)
  // Add state for the recharge dialog
  const [showRechargeDialog, setShowRechargeDialog] = useState<boolean>(false)
  // Add state for login dialog
  const [showLoginDialog, setShowLoginDialog] = useState<boolean>(false)
  // Add state for credits loading
  const [isLoadingCredits, setIsLoadingCredits] = useState(false)
  // Add state for generation history
  const [generationHistory, setGenerationHistory] = useState<any[]>([])
  
  // Get session from next-auth
  const { data: session, status } = useSession()
  
  // 保留这些引用，因为它们在其他地方被使用
  // Reference to the current generation
  const currentGenerationRef = React.useRef<HTMLDivElement>(null)
  // Reference to the scroll container
  const scrollContainerRef = React.useRef<HTMLDivElement>(null)
  const textareaRef = React.useRef<HTMLTextAreaElement>(null)
  const pcTextareaRef = React.useRef<HTMLTextAreaElement>(null)

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/categories', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        })
        
        if (!response.ok) {
          throw new Error(`Error: ${response.status}`)
        }
        
        const data = await response.json()
        if (data.length > 0) {
          setSelectedCategory(data[0].id)
        }
      } catch (error) {
        console.error("Error fetching categories:", error)
      }
    }
    
    fetchCategories()
  }, [])

  // Function to fetch user credits
  const getCreditsForUser = async () => {
    if (status !== "authenticated" || !session?.user) {
      setUserCredits(0)
      return null
    }
    
    try {
      setIsLoadingCredits(true)
      
      const response = await fetch('/api/user/credits', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        cache: 'no-store'
      })
      
      if (!response.ok) {
        throw new Error(`Error: ${response.status}`)
      }
      
      const data = await response.json()
      const credits = data.left_credits || 0
      setUserCredits(credits)
      return credits
    } catch (error) {
      console.error("Error fetching credits:", error)
      setUserCredits(0)
      return null
    } finally {
      setIsLoadingCredits(false)
    }
  }

  // Simplified refresh credits function
  const refreshCredits = async (e?: React.MouseEvent) => {
    if (e) e.stopPropagation();
    toast.info("Refreshing credits...")
    const result = await getCreditsForUser()
    if (result !== null) {
      toast.success(`Credits updated: ${result}`)
    } else {
      toast.error("Failed to update credits")
    }
  }

  // Update effect to fetch credits when session changes
  useEffect(() => {
    if (status === "authenticated" && session?.user) {
      getCreditsForUser()
    } else {
      setUserCredits(0)
    }
  }, [status, session?.user])

  const handleGenerationCountChange = (value: number[]) => {
    setGenerationCount(value[0])
  }

  const getDimensions = (ratio: string) => {
    switch (ratio) {
      case "1:1":
        return { width: 1024, height: 1024 }
      case "2:3":
        return { width: 768, height: 1152 }
      case "3:2":
        return { width: 1152, height: 768 }
      default:
        return { width: 1024, height: 1024 }
    }
  }

  // 修改handleOptimizePrompt函数，保存所有优化提示词但也自动应用第一个
  const handleOptimizePrompt = async () => {
    if (!prompt.trim()) {
      toast.error("Please enter a prompt")
      return
    }

    setIsOptimizing(true)
    try {
      const response = await fetch("/api/zhiti", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          instruction: prompt,
        }),
      })

      if (!response.ok) {
        throw new Error("Prompt optimization failed")
      }

      const data = await response.json()
      const prompts = data.content.split("\n").filter((p: string) => p.trim())
      
      // 保存所有优化提示词
      setOptimizedPrompts(prompts)
      
      // 自动应用第一个提示词
      if (prompts.length > 0) {
        setPrompt(prompts[0])
        
        // 更新输入框高度
        setTimeout(() => {
          if (pcTextareaRef.current) {
            pcTextareaRef.current.style.height = '250px'
          }
          
          if (textareaRef.current) {
            textareaRef.current.style.height = 'auto'
            textareaRef.current.style.height = `${Math.max(textareaRef.current.scrollHeight, 160)}px`
          }
        }, 10)
      }
      
      toast.success(`生成了 ${prompts.length} 个优化提示词`)
    } catch (error) {
      console.error("Optimization failed:", error)
      toast.error("Prompt optimization failed")
    } finally {
      setIsOptimizing(false)
    }
  }

  const handleOptimizedPromptClick = (optimizedPrompt: string) => {
    setSelectedOptimizedPrompt(optimizedPrompt)
    setPrompt(optimizedPrompt)
  }

  const handleCopyPrompt = (promptText: string) => {
    navigator.clipboard.writeText(promptText)
      .then(() => {
        toast.success("Prompt copied to clipboard")
      })
      .catch((error) => {
        console.error("Failed to copy: ", error)
        toast.error("Failed to copy prompt")
      })
  }

  const handleClearPrompt = () => {
    setPrompt("")
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = 'min-content'
    }
  }
  
  // 修改adjustTextareaHeight函数确保不会缩小高度
  const adjustTextareaHeight = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setPrompt(e.target.value)
    
    // 固定PC端输入框高度为250px
    if (pcTextareaRef.current) {
      pcTextareaRef.current.style.height = '250px'
    }
    
    // 固定移动端输入框最小高度为160px
    if (textareaRef.current) {
      const minHeight = 160
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = `${Math.max(textareaRef.current.scrollHeight, minHeight)}px`
    }
  }
  
  // 初始化textarea高度
  useEffect(() => {
    // 确保PC端输入框高度固定为250px
    if (pcTextareaRef.current) {
      pcTextareaRef.current.style.height = '250px'
    }
    
    // 确保移动端输入框最小高度为160px
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = `${Math.max(textareaRef.current.scrollHeight, 160)}px`
    }
  }, [])

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setSelectedImage(file)
    }
  }

  // Simplified handle generate function
  const handleGenerate = async () => {
    if (isGenerating || !prompt.trim()) return
    
    // Check if user is logged in
    if (status !== "authenticated") {
      setShowLoginDialog(true)
      return
    }
    
    // Check if user has enough credits
    const requiredCredits = creditCost * generationCount
    if (userCredits < requiredCredits) {
      setShowRechargeDialog(true)
      return
    }
    
    setIsGenerating(true)
    
    // Create a loading placeholder in the history
    const loadingId = `loading-${Date.now()}`
    const loadingGeneration = {
      id: loadingId,
      prompt: prompt,
      images: [],
      timestamp: new Date(),
      isLoading: true
    }
    
    // Add the loading placeholder to history
    setGenerationHistory(prev => [...prev, loadingGeneration])
    // 清空已生成的图片
    setGeneratedImages([])
    
    try {
      const dimensions = getDimensions(selectedAspectRatio)
      
      // 准备请求参数
      const requestParams = {
        prompt: prompt,
        width: dimensions.width,
        height: dimensions.height,
        count: generationCount,
        model: selectedModel,
        seed: Math.floor(Math.random() * 1000000),
        categoryId: selectedCategory,
        useCredits: true,
        creditCost: creditCost 
      };
      
      // 使用fetch POST请求
      const fetchResponse = await fetch("/api/pic/zidong", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestParams),
      });
      
      // 检查非SSE响应
      const contentType = fetchResponse.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        // 普通JSON响应
        const jsonData = await fetchResponse.json();
        
        if (!fetchResponse.ok) {
          throw new Error(jsonData.error || "生成图片失败");
        }
        
        // 更新积分
        if (jsonData.remainingCredits !== undefined) {
          setUserCredits(jsonData.remainingCredits);
        }
        
        // 提取并处理图片URLs
        try {
          const imageUrls = extractImageUrls(jsonData);
          
          // 更新状态
          setGeneratedImages(imageUrls);
          updateGenerationHistory(loadingId, imageUrls);
          
          // 回调
          if (onGenerate) onGenerate(imageUrls);
          
          toast.success("图片生成成功!");
        } catch (error) {
          console.error("处理图片URL出错:", error);
          toast.error(error instanceof Error ? error.message : "处理生成的图片失败");
          setGenerationHistory(prev => prev.filter(item => item.id !== loadingId));
        }
        
        setIsGenerating(false);
        return;
      }
      
      // 处理SSE流式响应
      await handleStreamResponse(fetchResponse, loadingId);
      
    } catch (error) {
      console.error("Generation failed:", error);
      toast.error(error instanceof Error ? error.message : "Image generation failed");
      
      // Remove the loading state from history on error
      setGenerationHistory(prev => prev.filter(item => item.id !== loadingId));
      setIsGenerating(false);
    }
  }

  // Helper function to extract image URLs from any response format
  const extractImageUrls = (data: any): string[] => {
    if (!data) return [];
    
    // Helper function to parse potentially JSON string URLs
    const parseImageUrl = (url: string): string => {
      try {
        const parsed = JSON.parse(url);
        // If it's an array, return the first item
        if (Array.isArray(parsed) && parsed.length > 0) {
          return parsed[0];
        }
        // If it's a string after parsing, return it
        if (typeof parsed === 'string') {
          return parsed;
        }
        // Fallback
        return url;
      } catch (e) {
        // If it's not valid JSON, return the original string
        return url;
      }
    };
    
    // Handle different data formats in priority order
    if (data.r2Urls && Array.isArray(data.r2Urls) && data.r2Urls.length > 0) {
      return data.r2Urls.map((url: string) => parseImageUrl(url));
    }
    
    if (data.r2Url) {
      return [parseImageUrl(data.r2Url)];
    }
    
    if (data.images && Array.isArray(data.images) && data.images.length > 0) {
      return data.images.map((url: string) => parseImageUrl(url));
    }
    
    if (data.imageUrls && Array.isArray(data.imageUrls)) {
      return data.imageUrls.map((url: string) => parseImageUrl(url));
    }
    
    if (data.imageUrl) {
      return [parseImageUrl(data.imageUrl)];
    }
    
    if (Array.isArray(data) && data.length > 0) {
      return data.map(item => typeof item === 'string' ? parseImageUrl(item) : '');
    }
    
    return [];
  }

  // Optimized streaming response handler
  const handleStreamResponse = async (response: Response, loadingId: string): Promise<void> => {
    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error("无法创建响应流读取器");
    }
    
    // 解码器
    const decoder = new TextDecoder();
    
    // 存储已生成的图片URL
    const generatedImages: string[] = [];
    
    // 处理流式响应
    let buffer = '';
    let isComplete = false;
    
    while (!isComplete) {
      const { done, value } = await reader.read();
      
      if (done) {
        break;
      }
      
      // 解码并添加到缓冲区
      buffer += decoder.decode(value, { stream: true });
      
      // 处理缓冲区中的每个事件
      const events = buffer.split('\n\n');
      // 保留最后一个不完整的事件
      buffer = events.pop() || '';
      
      for (const eventText of events) {
        if (!eventText.trim()) continue;
        
        // 解析事件和数据
        const lines = eventText.split('\n');
        let eventType = '';
        let eventData = '';
        
        for (const line of lines) {
          if (line.startsWith('event:')) {
            eventType = line.substring(6).trim();
          } else if (line.startsWith('data:')) {
            eventData = line.substring(5).trim();
          }
        }
        
        if (!eventType || !eventData) continue;
        
        try {
          const eventDataObj = JSON.parse(eventData);
          
          switch (eventType) {
            case 'start':
              // 任务启动事件
              console.log('图片生成任务已启动');
              break;
              
            case 'progress':
              // 进度更新事件
              toast.info(`生成进度: ${eventDataObj.message}`, {
                id: `progress-${loadingId}`, // 使用固定ID更新同一个toast
              });
              
              // 更新生成历史记录中的进度
              setGenerationHistory(prev => {
                const newHistory = [...prev];
                const index = newHistory.findIndex(item => item.id === loadingId);
                if (index !== -1) {
                  newHistory[index] = {
                    ...newHistory[index],
                    progress: eventDataObj.progress || 0
                  };
                }
                return newHistory;
              });
              
              break;
              
            case 'image': {
              // 获取图片URL - 优先使用R2 URL
              const imageUrl = eventDataObj.r2Url || eventDataObj.imageUrl;
              if (!imageUrl) continue;
              
              // 添加到生成的图片列表
              generatedImages.push(imageUrl);
              
              // 更新UI展示 - 使用函数方式保证拿到最新状态
              setGeneratedImages([...generatedImages]);
              
              // 实时更新历史记录
              updateGenerationHistory(loadingId, [...generatedImages]);
              
              // 计算完成进度
              const completed = eventDataObj.completed || generatedImages.length;
              const total = eventDataObj.total || generationCount;
              
              // 通知，使用固定ID避免多条通知
              toast.success(`已生成第 ${completed} 张图片，共 ${total} 张`, {
                id: `image-progress-${loadingId}`,
              });
              
              break;
            }
              
            case 'complete':
              // 任务完成事件
              console.log('图片生成任务已完成');
              isComplete = true;
              
              // 更新用户积分
              if (eventDataObj.remainingCredits !== undefined) {
                setUserCredits(eventDataObj.remainingCredits);
              }
              
              // 更新历史记录，标记为已完成但保留在当前位置显示
              setGenerationHistory(prev => {
                const newHistory = [...prev];
                const index = newHistory.findIndex(item => item.id === loadingId);
                if (index !== -1) {
                  newHistory[index] = {
                    ...newHistory[index],
                    isLoading: false,
                    images: [...generatedImages], // 确保最新的图片数据
                    showInPlace: true, // 保持在原位置显示
                    completedAt: new Date() // 记录完成时间
                  };
                }
                return newHistory;
              });
              
              toast.success("所有图片生成成功!", {
                id: `complete-${loadingId}`,
              });
              
              // 在完成后的一段时间后，允许启动新的生成任务
              setTimeout(() => {
                setIsGenerating(false);
              }, 500);
              
              break;
              
            case 'error':
              // 错误事件
              console.error('生成错误:', eventDataObj.message);
              
              // 如果是部分错误，显示警告而不是错误
              if (eventDataObj.partial && generatedImages.length > 0) {
                toast.warning(`生成了 ${eventDataObj.completed || generatedImages.length} 张图片后遇到错误`);
                isComplete = true;
              } else {
                toast.error(eventDataObj.message || "图片生成失败");
                // 删除加载状态
                setGenerationHistory(prev => prev.filter(item => item.id !== loadingId));
              }
              
              setIsGenerating(false);
              isComplete = true;
              break;
          }
        } catch (e) {
          console.error('处理事件数据出错:', e);
        }
      }
    }
    
    // 流程结束，确保状态更新
    if (isGenerating) {
      setIsGenerating(false);
      
      if (generatedImages.length > 0) {
        toast.success("图片生成成功!");
      } else {
        toast.error("生成过程意外终止");
        setGenerationHistory(prev => prev.filter(item => item.id !== loadingId));
      }
    }
  }

  // Modified generation history update function
  const updateGenerationHistory = (loadingId: string, imageUrls: string[]) => {
    setGenerationHistory(prev => {
      const newHistory = [...prev];
      // 查找loading状态的项
      const loadingIndex = newHistory.findIndex(item => item.id === loadingId);
      
      if (loadingIndex !== -1) {
        // 更新loading项中的图片数组
        newHistory[loadingIndex] = {
          ...newHistory[loadingIndex],
          images: imageUrls,
          isLoading: imageUrls.length < generationCount, // 只有收到所有图片才完成加载
          showInPlace: true // 确保在原位置显示
        };
      }
      
      return newHistory;
    });
  }

  // Add function to handle recharge
  const handleRecharge = () => {
    // Close the dialog
    setShowRechargeDialog(false)
    // Redirect to payment page or open payment modal
    window.location.href = "/pricing"
  }

  // Simplified login dialog
  const LoginDialog = () => (
    <Dialog open={showLoginDialog} onOpenChange={setShowLoginDialog}>
      <DialogContent className="bg-gray-900 border-gray-700 text-white">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-amber-400">Login Required</DialogTitle>
          <DialogDescription className="text-gray-300">
            Please login to generate images and access all features.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <Card className="bg-gradient-to-r from-gray-800 to-gray-900 border-gray-700 p-6 flex flex-col items-center justify-center shadow-md">
            <div className="text-lg text-white font-medium flex items-center mb-4">Join our creative community</div>
            <div className="text-sm text-gray-300 mb-6 text-center">
              Create an account to access AI image generation, save your creations, and unlock premium features.
            </div>
            <Button 
              className="w-full bg-emerald-600 hover:bg-emerald-700 text-white mb-2"
              onClick={() => {
                setShowLoginDialog(false);
                signIn("google");
              }}
            >
              Sign in with Google
            </Button>
          </Card>
        </div>
        <DialogFooter className="flex flex-col sm:flex-row gap-2">
          <Button variant="ghost" className="text-gray-400 hover:text-gray-200" onClick={() => setShowLoginDialog(false)}>
            Continue Browsing
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );

  // Recharge Dialog Component
  const RechargeDialog = () => (
    <Dialog open={showRechargeDialog} onOpenChange={setShowRechargeDialog}>
      <DialogContent className="bg-gray-900 border-gray-700 text-white">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-amber-400">Insufficient Credits</DialogTitle>
          <DialogDescription className="text-gray-300">
            You need {generationCount * creditCost} credits to generate {generationCount} {generationCount === 1 ? 'image' : 'images'}, but you only have {userCredits} credits.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <Card className="bg-gradient-to-r from-purple-900/70 to-blue-900/70 border-indigo-700 p-6 flex flex-col items-center justify-center shadow-md">
            <Sparkles className="h-8 w-8 mb-3 text-indigo-300" />
            <div className="text-lg text-indigo-200 font-medium flex items-center mb-2">Recharge Your Credits</div>
            <div className="text-sm flex items-center mb-3 text-gray-200">
              Get <span className="text-indigo-300 mx-1 font-bold">50+ FREE</span> credits with any package
            </div>
            <div className="text-sm text-gray-300 mb-4">Limited time offer - Best Value!</div>
            <Button 
              className="w-full bg-indigo-600 hover:bg-indigo-700 text-white"
              onClick={handleRecharge}
            >
              Recharge Now
            </Button>
          </Card>
        </div>
        <DialogFooter className="flex flex-col sm:flex-row gap-2">
          <Button variant="outline" className="border-gray-600 text-gray-400 hover:text-gray-200" onClick={() => setShowRechargeDialog(false)}>
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );

  // Main render function - 使用shadcn组件替换自定义容器
  return (
    <Card className="w-full border-0 shadow-none rounded-none overflow-visible">
      {/* PC Layout */}
      <div className="hidden md:block w-full">
        {/* 顶部信息栏 */}
        <CardHeader className="p-3 flex flex-row items-center justify-between bg-gray-900/80 border-b border-gray-800">
          <h2 className="text-lg font-bold text-white">AI Image Generator</h2>
          
          <div className="flex items-center space-x-3">
            {/* Premium label */}
            {status === "authenticated" && (
              <Badge variant="outline" className="bg-gradient-to-r from-purple-900/70 to-blue-900/70 border-indigo-700 px-2 py-1 flex items-center">
                <Sparkles className="h-3.5 w-3.5 mr-1 text-indigo-300 flex-shrink-0" />
                <span className="text-xs text-indigo-300 font-medium mr-1 flex-shrink-0">
                  PREMIUM
                </span>
                <span className="text-xs text-gray-300">
                  <span className="text-indigo-300 font-bold">50+ FREE</span>
                </span>
              </Badge>
            )}
            
            {/* Credits Display */}
            <Badge variant="outline" className="px-3 py-1 bg-amber-900/50 text-base font-bold text-amber-400 border-amber-700/50 flex items-center cursor-pointer" onClick={refreshCredits}>
              <Zap className="h-4 w-4 mr-1.5" />
              {isLoadingCredits ? (
                <span className="inline-block animate-pulse">...</span>
              ) : (
                userCredits
              )}
              <RefreshCw className="h-3.5 w-3.5 ml-1.5 text-amber-300/70 hover:text-amber-300" />
            </Badge>
            
            <Button
              variant="outline"
              size="sm"
              className="bg-emerald-900/70 text-emerald-100 border-emerald-700 hover:bg-emerald-800"
            >
              Ver 1.5
              <span className="ml-2 text-xs bg-emerald-500 text-black px-1 rounded">NEW</span>
            </Button>
          </div>
        </CardHeader>
        
        {/* Main Content Area */}
        <CardContent className="p-0 bg-gradient-to-b from-gray-900/40 to-gray-950">
          {/* 控制面板区域 */}
          <Card className="rounded-none shadow-none border-0 border-b border-gray-800 bg-transparent">
            <CardContent className="p-4">
              <div className="flex gap-4">
                {/* Creative Description Section */}
                <div className="flex-1">
                  <Card className="bg-gray-900 border-gray-700 p-3 relative shadow-md">
                    <textarea
                      ref={pcTextareaRef}
                      value={prompt}
                      onChange={adjustTextareaHeight}
                      className="w-full bg-transparent border-none text-sm text-gray-300 focus:outline-none resize-none h-[250px] pr-2" // 确保PC端输入框固定高度为250px
                      placeholder="Describe the image you want to generate..."
                      style={{wordWrap: 'break-word', overflowWrap: 'break-word'}}
                    />
                    <div className="absolute bottom-2 right-2 flex space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-emerald-400 hover:text-emerald-300"
                        onClick={handleOptimizePrompt}
                        disabled={isOptimizing}
                      >
                        {isOptimizing ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            <span className="font-medium tracking-wide">Enhancing...</span>
                          </>
                        ) : (
                          <>
                            <Sparkles className="mr-2 h-4 w-4" />
                            <span className="font-medium tracking-wide">Enhance</span>
                          </>
                        )}
                      </Button>
                      
                      {prompt.trim() && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-gray-400 hover:text-red-400"
                          onClick={handleClearPrompt}
                        >
                          <X className="mr-1 h-4 w-4" />
                          <span className="font-medium tracking-wide">Clear</span>
                        </Button>
                      )}
                    </div>
                    
                    {/* Display optimized prompts directly inside the input card */}
                    {optimizedPrompts.length > 0 && (
                      <div className="mt-3 pt-3 border-t border-gray-700">
                        <div className="mb-1">
                          <Label className="text-sm text-gray-400">优化提示词:</Label>
                        </div>
                        <div className="flex flex-wrap gap-2 mt-2">
                          {optimizedPrompts.map((optimizedPrompt, idx) => (
                            <Badge 
                              key={idx}
                              variant="outline"
                              className="py-1.5 px-2 bg-gray-800 hover:bg-gray-700 border-gray-700 text-gray-300 hover:text-emerald-300 flex gap-2 items-center cursor-pointer text-xs"
                              onClick={() => handleOptimizedPromptClick(optimizedPrompt)}
                            >
                              <span>{optimizedPrompt}</span>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-4 w-4 rounded-full text-gray-400 hover:text-emerald-400 hover:bg-emerald-900/30 flex items-center justify-center p-0"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleCopyPrompt(optimizedPrompt);
                                }}
                              >
                                <Copy className="h-3 w-3" />
                              </Button>
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </Card>
                </div>
                
                {/* Generate Button */}
                <div className="flex flex-col justify-end w-[140px]">
                  <Button
                    className="w-full h-14 bg-gradient-to-r from-emerald-500 to-cyan-500 hover:from-emerald-600 hover:to-cyan-600 text-white text-base font-semibold shadow-lg"
                    onClick={handleGenerate}
                    disabled={isGenerating || !prompt.trim()}
                  >
                    {isGenerating ? (
                      <>
                        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        Generate
                        <span className="ml-2 bg-white bg-opacity-20 px-2 py-0.5 rounded text-xs">{generationCount * creditCost}</span>
                      </>
                    )}
                  </Button>
                </div>
              </div>
              
              {/* Options Section */}
              <div className="mt-4 grid grid-cols-4 gap-4">
                {/* Model Selection */}
                <div className="flex flex-col space-y-1">
                  <Label className="text-sm text-gray-400">Model:</Label>
                  <Select value={selectedModel} onValueChange={setSelectedModel}>
                    <SelectTrigger className="w-full h-8 bg-gray-800 border-gray-700">
                      <SelectValue placeholder="Select Model" />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-700">
                      <SelectItem value="flux.schnell">Flux Schnell (Fast)</SelectItem>
                      <SelectItem value="flux">Flux (Standard)</SelectItem>
                      <SelectItem value="stable-diffusion">Stable Diffusion</SelectItem>
                      <SelectItem value="midjourney">Midjourney-like</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                {/* Image Ratio Section */}
                <div className="flex flex-col space-y-1">
                  <Label className="text-sm text-gray-400">Ratio:</Label>
                  <RadioGroup
                    value={selectedAspectRatio}
                    onValueChange={setSelectedAspectRatio}
                    className="flex space-x-2"
                  >
                    <div>
                      <RadioGroupItem value="1:1" id="pc-1:1" className="peer sr-only" />
                      <Label
                        htmlFor="pc-1:1"
                        className="flex flex-col items-center justify-between rounded-md border-2 border-gray-700 bg-gray-800 px-3 py-1 hover:bg-gray-700 hover:text-emerald-400 peer-data-[state=checked]:border-emerald-500 [&:has([data-state=checked])]:border-emerald-500 cursor-pointer"
                      >
                        <span className="text-sm">1:1</span>
                      </Label>
                    </div>
                    <div>
                      <RadioGroupItem value="2:3" id="pc-2:3" className="peer sr-only" />
                      <Label
                        htmlFor="pc-2:3"
                        className="flex flex-col items-center justify-between rounded-md border-2 border-gray-700 bg-gray-800 px-3 py-1 hover:bg-gray-700 hover:text-emerald-400 peer-data-[state=checked]:border-emerald-500 [&:has([data-state=checked])]:border-emerald-500 cursor-pointer"
                      >
                        <span className="text-sm">2:3</span>
                      </Label>
                    </div>
                    <div>
                      <RadioGroupItem value="3:2" id="pc-3:2" className="peer sr-only" />
                      <Label
                        htmlFor="pc-3:2"
                        className="flex flex-col items-center justify-between rounded-md border-2 border-gray-700 bg-gray-800 px-3 py-1 hover:bg-gray-700 hover:text-emerald-400 peer-data-[state=checked]:border-emerald-500 [&:has([data-state=checked])]:border-emerald-500 cursor-pointer"
                      >
                        <span className="text-sm">3:2</span>
                      </Label>
                    </div>
                  </RadioGroup>
                </div>
                
                {/* Generation Count */}
                <div className="flex flex-col space-y-1">
                  <Label className="text-sm text-gray-400">Count:</Label>
                  <div className="flex items-center">
                    <Slider
                      value={[generationCount]}
                      onValueChange={handleGenerationCountChange}
                      min={1}
                      max={4}
                      step={1}
                      className="flex-1 mr-2"
                    />
                    <span className="text-sm text-emerald-400 w-4">{generationCount}</span>
                  </div>
                </div>
                
                {/* Recommended Tags */}
                <div className="flex flex-col space-y-1">
                  <Label className="text-sm text-gray-400">Tags:</Label>
                  <div className="flex flex-wrap gap-1">
                    <Badge variant="outline" className="h-6 text-xs bg-gray-800 border-gray-700 text-emerald-300 hover:bg-gray-700 hover:text-emerald-200">
                      Chinese Style
                    </Badge>
                    <Badge variant="outline" className="h-6 text-xs bg-gray-800 border-gray-700 text-emerald-300 hover:bg-gray-700 hover:text-emerald-200">
                      CG Art
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 案例展示区域 */}
          <div className="p-4">
            {/* Add generation area directly above Results */}
            {isGenerating && (
              <div className="mb-8">
                <div className="flex items-center mb-4">
                  <h3 className="text-lg font-semibold text-white">Current Generation</h3>
                  <div className="ml-3 flex items-center">
                    <div className="h-2 w-2 rounded-full bg-emerald-500 animate-pulse mr-2" />
                    <span className="text-sm text-emerald-400">Generating images...</span>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                  {Array(generationCount).fill(0).map((_, i) => (
                    <GenerationPlaceholder key={`placeholder-${i}`} />
                  ))}
                </div>
              </div>
            )}
            
            {/* Show generated images section when available */}
            {!isGenerating && generatedImages.length > 0 && (
              <div className="mb-8">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">Generated Images</h3>
                  <Button variant="outline" size="sm" className="text-gray-300 border-gray-700">
                    Save All
                  </Button>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {generatedImages.map((imageUrl, idx) => (
                    <div 
                      key={`result-${idx}`} 
                      className="relative group overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      <div className="aspect-[3/4] bg-gray-900 overflow-hidden">
                        <img
                          src={imageUrl}
                          alt={`Generated image ${idx + 1}`}
                          className="w-full h-full object-cover transition-transform group-hover:scale-105"
                        />
                      </div>
                      
                      {/* 悬停时显示的信息 */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black via-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-4">
                        <p className="text-sm text-gray-200 line-clamp-2 mb-2">Image {idx + 1}</p>
                        <div className="flex justify-between items-center">
                          <div className="flex space-x-3">
                            <button className="text-white/80 hover:text-white">
                              <Heart size={18} />
                            </button>
                            <button className="text-white/80 hover:text-white">
                              <Download size={18} />
                            </button>
                          </div>
                          <div className="text-xs text-gray-400">{new Date().toLocaleDateString()}</div>
                        </div>
                      </div>
                      
                      {/* 永久可见的标签 */}
                      <div className="absolute top-2 right-2 text-xs bg-black bg-opacity-70 px-2 py-1 rounded-full text-emerald-400 font-medium">
                        生成
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 历史生成结果 - PC */}
            <div className="mt-8">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-white">Your Image History</h3>
                <span className="text-sm text-gray-400">From database</span>
              </div>
              <Results 
                userId={session?.user?.id}
                limit={20}
                isMobile={false}
                loading={isLoadingCredits}
                disableAutoFetch={false}
              />
            </div>
          </div>
        </CardContent>
      </div>

      {/* Mobile Layout */}
      <div className="md:hidden flex flex-col w-full">
        {/* 顶部信息 */}
        <CardHeader className="p-3 flex flex-row items-center justify-between bg-gray-900/90 border-b border-gray-800">
          <h2 className="text-lg font-bold text-white">AI Image Generator</h2>
          <div className="flex items-center space-x-2">
            {status === "authenticated" && (
              <Badge variant="outline" className="px-2 py-1 bg-amber-900/50 text-sm font-bold text-amber-400 border-amber-700/50 flex items-center">
                <Zap className="h-3 w-3 mr-1" />
                {userCredits}
              </Badge>
            )}
            <Button
              variant="outline"
              size="sm"
              className="bg-emerald-900/70 text-emerald-100 border-emerald-700 hover:bg-emerald-800"
            >
              Ver 1.5
            </Button>
          </div>
        </CardHeader>

        {/* 内容区域 */}
        <CardContent className="p-0 overflow-y-auto bg-gradient-to-b from-gray-900/40 to-gray-950 pb-20">
          {/* 控制区域 */}
          <Card className="rounded-none shadow-none border-0 border-b border-gray-800 bg-gray-900/50">
            <CardContent className="p-4">
              {/* 输入框 */}
              <Card className="bg-gray-900 border-gray-700 p-4 relative shadow-md mb-4">
                <textarea
                  ref={textareaRef}
                  value={prompt}
                  onChange={adjustTextareaHeight}
                  className="w-full bg-transparent border-none text-sm text-gray-300 focus:outline-none resize-none min-h-[160px] pb-14 pr-2" // 移动端输入框最小高度为160px
                  placeholder="Describe the image you want to generate..."
                  style={{wordWrap: 'break-word', overflowWrap: 'break-word'}}
                />
                <div className="absolute bottom-2 left-0 right-0 flex justify-between px-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-emerald-400 hover:text-emerald-300"
                    onClick={handleOptimizePrompt}
                    disabled={isOptimizing}
                  >
                    {isOptimizing ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        <span className="font-medium tracking-wide">Enhancing...</span>
                      </>
                    ) : (
                      <>
                        <Sparkles className="mr-2 h-4 w-4" />
                        <span className="font-medium tracking-wide">Enhance</span>
                      </>
                    )}
                  </Button>
                  
                  {prompt.trim() && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-gray-400 hover:text-red-400"
                      onClick={handleClearPrompt}
                    >
                      <X className="mr-1 h-4 w-4" />
                      <span className="font-medium tracking-wide">Clear</span>
                    </Button>
                  )}
                </div>
                
                {/* Display optimized prompts directly inside the input card for mobile */}
                {optimizedPrompts.length > 0 && (
                  <div className="mt-14 pt-3 border-t border-gray-700">
                    <div className="mb-1">
                      <Label className="text-sm text-gray-400">优化提示词:</Label>
                    </div>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {optimizedPrompts.map((optimizedPrompt, idx) => (
                        <Badge 
                          key={idx}
                          variant="outline"
                          className="py-1.5 px-2 bg-gray-800 hover:bg-gray-700 border-gray-700 text-gray-300 hover:text-emerald-300 flex gap-2 items-center cursor-pointer text-xs"
                          onClick={() => handleOptimizedPromptClick(optimizedPrompt)}
                        >
                          <span>{optimizedPrompt}</span>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-4 w-4 rounded-full text-gray-400 hover:text-emerald-400 hover:bg-emerald-900/30 flex items-center justify-center p-0"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleCopyPrompt(optimizedPrompt);
                            }}
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </Card>

              {/* 模型选择 */}
              <div className="mb-4">
                <div className="flex items-center mb-2">
                  <Label className="text-sm text-gray-400 mr-2">Model:</Label>
                  <Select value={selectedModel} onValueChange={setSelectedModel}>
                    <SelectTrigger className="w-full h-8 bg-gray-800 border-gray-700">
                      <SelectValue placeholder="Select Model" />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-700">
                      <SelectItem value="flux.schnell">Flux Schnell (Fast)</SelectItem>
                      <SelectItem value="flux">Flux (Standard)</SelectItem>
                      <SelectItem value="stable-diffusion">Stable Diffusion</SelectItem>
                      <SelectItem value="midjourney">Midjourney-like</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* 图片比例 */}
              <div className="mb-4">
                <Label className="text-sm text-gray-400 block mb-2">Image Ratio:</Label>
                <RadioGroup
                  value={selectedAspectRatio}
                  onValueChange={setSelectedAspectRatio}
                  className="grid grid-cols-3 gap-2"
                >
                  <div>
                    <RadioGroupItem value="1:1" id="mobile-1:1" className="peer sr-only" />
                    <Label
                      htmlFor="mobile-1:1"
                      className="flex flex-col items-center justify-between rounded-md border-2 border-gray-700 bg-gray-800 p-2 hover:bg-gray-700 hover:text-emerald-400 peer-data-[state=checked]:border-emerald-500 [&:has([data-state=checked])]:border-emerald-500 cursor-pointer"
                    >
                      <span className="text-sm">1:1</span>
                      <span className="text-xs text-gray-400">Square</span>
                    </Label>
                  </div>
                  <div>
                    <RadioGroupItem value="2:3" id="mobile-2:3" className="peer sr-only" />
                    <Label
                      htmlFor="mobile-2:3"
                      className="flex flex-col items-center justify-between rounded-md border-2 border-gray-700 bg-gray-800 p-2 hover:bg-gray-700 hover:text-emerald-400 peer-data-[state=checked]:border-emerald-500 [&:has([data-state=checked])]:border-emerald-500 cursor-pointer"
                    >
                      <span className="text-sm">2:3</span>
                      <span className="text-xs text-gray-400">Portrait</span>
                    </Label>
                  </div>
                  <div>
                    <RadioGroupItem value="3:2" id="mobile-3:2" className="peer sr-only" />
                    <Label
                      htmlFor="mobile-3:2"
                      className="flex flex-col items-center justify-between rounded-md border-2 border-gray-700 bg-gray-800 p-2 hover:bg-gray-700 hover:text-emerald-400 peer-data-[state=checked]:border-emerald-500 [&:has([data-state=checked])]:border-emerald-500 cursor-pointer"
                    >
                      <span className="text-sm">3:2</span>
                      <span className="text-xs text-gray-400">Landscape</span>
                    </Label>
                  </div>
                </RadioGroup>
              </div>

              {/* 生成数量 */}
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <Label className="text-sm text-gray-400">Generation Count:</Label>
                  <span className="text-sm text-emerald-400">{generationCount}</span>
                </div>
                <Slider
                  value={[generationCount]}
                  onValueChange={handleGenerationCountChange}
                  min={1}
                  max={4}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-400 mt-1">
                  <span>1 image</span>
                  <span>4 images</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 案例展示区域 */}
          <div className="p-4">
            {/* Add generation area directly above Results for mobile */}
            {isGenerating && (
              <div className="mb-6">
                <div className="flex items-center mb-3">
                  <h3 className="text-base font-semibold text-white">Current Generation</h3>
                  <div className="ml-2 flex items-center">
                    <div className="h-2 w-2 rounded-full bg-emerald-500 animate-pulse mr-1" />
                    <span className="text-xs text-emerald-400">Processing...</span>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-3 mb-4">
                  {Array(generationCount).fill(0).map((_, i) => (
                    <GenerationPlaceholder key={`mobile-placeholder-${i}`} />
                  ))}
                </div>
              </div>
            )}
            
            {/* Show generated images section when available for mobile */}
            {!isGenerating && generatedImages.length > 0 && (
              <div className="mb-6">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-base font-semibold text-white">Results</h3>
                  <Button variant="outline" size="sm" className="text-xs text-gray-300 border-gray-700 py-0 h-7">
                    Save All
                  </Button>
                </div>
                
                <div className="grid grid-cols-2 gap-3 mb-4">
                  {generatedImages.map((imageUrl, idx) => (
                    <div 
                      key={`mobile-result-${idx}`} 
                      className="relative group overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      <div className="aspect-[3/4] bg-gray-900 overflow-hidden">
                        <img
                          src={imageUrl}
                          alt={`Generated image ${idx + 1}`}
                          className="w-full h-full object-cover transition-transform group-hover:scale-105"
                        />
                      </div>
                      
                      {/* 悬停时显示的信息 - 在移动端轻触会显示 */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black via-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-3">
                        <div className="flex justify-between items-center">
                          <div className="flex space-x-2">
                            <button className="text-white/80 hover:text-white">
                              <Download size={16} />
                            </button>
                          </div>
                          <div className="text-xs text-gray-400">{idx + 1}</div>
                        </div>
                      </div>
                      
                      {/* 永久可见的标签 */}
                      <div className="absolute top-2 right-2 text-xs bg-black bg-opacity-70 px-1.5 py-0.5 rounded-full text-emerald-400 font-medium text-[10px]">
                        生成
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 历史生成结果 - Mobile */}
            <div className="mt-6">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-base font-semibold text-white">Image Library</h3>
                <span className="text-xs text-gray-400">From database</span>
              </div>
              <Results 
                userId={session?.user?.id}
                limit={20}
                isMobile={true}
                loading={isLoadingCredits}
                disableAutoFetch={false}
              />
            </div>
          </div>
          
          {/* 生成按钮固定在底部 */}
          <CardFooter className="fixed left-0 right-0 bottom-0 p-3 bg-gray-900/95 border-t border-gray-700 shadow-lg">
            <Button
              className="w-full h-12 bg-gradient-to-r from-emerald-500 to-cyan-500 hover:from-emerald-600 hover:to-cyan-600 text-white font-medium shadow-lg"
              onClick={handleGenerate}
              disabled={isGenerating || !prompt.trim()}
            >
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  Generate Now
                  <span className="ml-2 text-xs bg-white bg-opacity-20 px-1 rounded">{generationCount * creditCost} Credits</span>
                </>
              )}
            </Button>
          </CardFooter>
        </CardContent>
      </div>

      {/* Add Login Dialog */}
      <LoginDialog />

      {/* Add Recharge Dialog */}
      <RechargeDialog />
    </Card>
  )
}

export default Zidong 