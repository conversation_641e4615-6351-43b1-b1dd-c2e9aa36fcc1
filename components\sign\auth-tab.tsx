"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import SignForm from "./form";
import SignUpForm from "./signup-form";
import { useTranslations } from "next-intl";
import { useState } from "react";

interface AuthTabProps {
  initialTab?: "signin" | "signup";
}

export default function AuthTab({ initialTab = "signin" }: AuthTabProps) {
  const t = useTranslations();
  const [currentTab, setCurrentTab] = useState<string>(initialTab);

  const toggleTab = () => {
    setCurrentTab(currentTab === "signin" ? "signup" : "signin");
  };

  return (
    <Tabs defaultValue={initialTab} value={currentTab} onValueChange={setCurrentTab}>
      <TabsList className="grid w-full grid-cols-2">
        <TabsTrigger value="signin">{t("sign_modal.sign_in_title")}</TabsTrigger>
        <TabsTrigger value="signup">{t("sign_modal.sign_up_title")}</TabsTrigger>
      </TabsList>
      <TabsContent value="signin">
        <SignForm />
      </TabsContent>
      <TabsContent value="signup">
        <SignUpForm onToggle={toggleTab} />
      </TabsContent>
    </Tabs>
  );
} 