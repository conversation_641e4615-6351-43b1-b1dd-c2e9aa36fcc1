import { NextResponse } from "next/server";
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { createClient } from '@supabase/supabase-js';
import { getUserCredits, decreaseCredits, CreditsTransType, CreditsAmount } from "@/services/credit";
export const runtime = "edge";
// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

// Initialize R2 client
const s3 = new S3Client({
  region: "auto",
  endpoint: `https://${process.env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.CLOUDFLARE_SECRET_ACCESS_KEY || '',
  },
});

// Upload image to R2
async function uploadImageToR2(imageUrl: string): Promise<string> {
  try {
    console.log('Uploading to R2:', imageUrl);
    const response = await fetch(imageUrl);
    if (!response.ok) throw new Error('Failed to fetch image');
    
    const imageBuffer = await response.arrayBuffer();
    const fileName = `pollinations-${Date.now()}.png`;

    await s3.send(
      new PutObjectCommand({
        Bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
        Key: fileName,
        Body: Buffer.from(imageBuffer),
        ContentType: 'image/png',
      })
    );

    const publicUrl = `${process.env.NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_DOMAIN}/${fileName}`;
    console.log('Uploaded to R2:', publicUrl);
    return publicUrl;
  } catch (error) {
    console.error('R2 upload error:', error);
    throw error;
  }
}

export async function POST(req: Request) {
  try {
    const { imageUrl, prompt, guestId, width, height, seed, model } = await req.json();

    if (!imageUrl) {
      return NextResponse.json(
        { error: "Image URL is required" },
        { status: 400 }
      );
    }

    if (!guestId) {
      return NextResponse.json(
        { error: "User ID is required" },
        { status: 400 }
      );
    }

    // Check if user has enough credits
    const userCredits = await getUserCredits(guestId);
    if (userCredits.left_credits < CreditsAmount.ImageGenerationCost) {
      return NextResponse.json(
        { 
          error: "Not enough credits to save image", 
          credits: userCredits.left_credits 
        },
        { status: 403 }
      );
    }

    // Upload to R2
    const r2Url = await uploadImageToR2(imageUrl);
    console.log('R2 URL:', r2Url);

    // Create record in database
    const { error } = await supabase
      .from('image_generations')
      .insert({
        user_uuid: guestId,
        prompt: prompt,
        images: [r2Url], // Store as array for consistency with other image generations
        is_public: false,
        width: width,
        height: height,
        seed: seed,
        model: model || 'default',
        generation_type: 'pollinations'
      });

    if (error) {
      console.error('Supabase insert error:', error);
      throw new Error('Failed to save image record');
    }

    // Deduct user credits
    await decreaseCredits({
      user_uuid: guestId,
      trans_type: CreditsTransType.SystemAdd,
      credits: CreditsAmount.ImageGenerationCost
    });

    console.log('Image saved successfully, credits deducted');

    return NextResponse.json({ 
      success: true,
      r2_url: r2Url,
      credits_consumed: CreditsAmount.ImageGenerationCost,
      credits_remaining: userCredits.left_credits - CreditsAmount.ImageGenerationCost
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to save image" },
      { status: 500 }
    );
  }
} 