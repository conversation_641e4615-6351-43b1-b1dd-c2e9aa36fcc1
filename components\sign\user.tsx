"use client";

import * as React from "react";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import Link from "next/link";
import { User } from "@/types/user";
import { signOut } from "next-auth/react";
import { useTranslations } from "next-intl";
import { User as UserIcon, Settings, LogOut } from "lucide-react";

export default function SignUser({ user }: { user: User }) {
  const t = useTranslations();
  
  // 获取用户昵称的首字母作为备用头像
  const getInitials = (name: string) => {
    return name ? name.charAt(0).toUpperCase() : '?';
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Avatar className="cursor-pointer hover:ring-2 hover:ring-primary/20 transition-all">
          {user.avatar_url ? (
            <AvatarImage src={user.avatar_url} alt={user.nickname} />
          ) : null}
          <AvatarFallback className="bg-primary/10 text-primary">
            {getInitials(user.nickname || user.email)}
          </AvatarFallback>
        </Avatar>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="mx-4 w-56">
        <div className="flex flex-col gap-1 px-2 py-2">
          <p className="text-sm font-medium text-center truncate">
            {user.nickname || user.email?.split('@')[0]}
          </p>
          <p className="text-xs text-muted-foreground text-center truncate">
            {user.email}
          </p>
        </div>
        <DropdownMenuSeparator />

        <DropdownMenuItem className="gap-2 cursor-pointer">
          <UserIcon className="w-4 h-4" />
          <Link href="/my-orders" className="flex-1">
            {t("user.user_center")}
          </Link>
        </DropdownMenuItem>

        <DropdownMenuItem className="gap-2 cursor-pointer">
          <Settings className="w-4 h-4" />
          <Link href="/admin/users" target="_blank" className="flex-1">
            {t("user.admin_system")}
          </Link>
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuItem
          className="gap-2 cursor-pointer text-red-500 focus:text-red-500"
          onClick={() => signOut()}
        >
          <LogOut className="w-4 h-4" />
          <span>{t("user.sign_out")}</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
