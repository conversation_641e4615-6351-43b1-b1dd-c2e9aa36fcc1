// Test script for Replicate API Token
// Run with: node debug/test-replicate-token.js

const fetch = require('node-fetch');

async function testReplicateToken() {
  const token = process.env.REPLICATE_API_TOKEN;
  
  if (!token) {
    console.error('❌ REPLICATE_API_TOKEN environment variable is not set');
    return;
  }

  console.log('🔑 Testing Replicate API token...');
  console.log('Token:', token.substring(0, 10) + '...');

  try {
    // Test account endpoint
    const response = await fetch('https://api.replicate.com/v1/account', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('Response status:', response.status);
    
    if (response.status === 401) {
      console.error('❌ Invalid API token - 401 Unauthorized');
      return;
    }

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API error:', response.status, errorText);
      return;
    }

    const data = await response.json();
    console.log('✅ Token is valid!');
    console.log('Account info:', JSON.stringify(data, null, 2));

    // Test model access
    console.log('\n🔍 Testing model access...');
    const modelResponse = await fetch('https://api.replicate.com/v1/models/minimax/image-01', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (modelResponse.ok) {
      const modelData = await modelResponse.json();
      console.log('✅ Model access successful!');
      console.log('Model:', modelData.name);
      console.log('Description:', modelData.description);
    } else {
      console.error('❌ Model access failed:', modelResponse.status);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testReplicateToken();
