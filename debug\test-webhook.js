// 测试 webhook 处理器
// 运行: node debug/test-webhook.js

const fetch = require('node-fetch');

async function testWebhook() {
  console.log('🧪 测试 Webhook 处理器...\n');

  // 模拟 Replicate webhook 数据
  const mockWebhookData = {
    id: 'test-prediction-id-123',
    status: 'succeeded',
    input: {
      prompt: 'A beautiful sunset over mountains, photorealistic',
      aspect_ratio: '3:4'
    },
    output: [
      'https://replicate.delivery/pbxt/example-image.png'
    ],
    created_at: new Date().toISOString(),
    completed_at: new Date().toISOString()
  };

  try {
    console.log('📤 发送模拟 webhook 数据...');
    console.log('📦 数据:', JSON.stringify(mockWebhookData, null, 2));

    const response = await fetch('http://localhost:3000/api/replicate/webhook', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(mockWebhookData)
    });

    console.log('📡 响应状态:', response.status);
    
    const responseText = await response.text();
    console.log('📡 响应内容:', responseText);

    if (response.ok) {
      console.log('✅ Webhook 测试成功!');
    } else {
      console.log('❌ Webhook 测试失败');
      if (response.status === 404) {
        console.log('💡 提示: 可能是找不到对应的 prompt 记录');
      }
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.log('💡 确保开发服务器正在运行 (npm run dev)');
  }
}

async function testWebhookEndpoint() {
  console.log('\n🔍 测试 webhook 端点是否可访问...');
  
  try {
    const response = await fetch('http://localhost:3000/api/replicate/webhook', {
      method: 'GET'
    });
    
    console.log('📡 GET 响应状态:', response.status);
    
    if (response.status === 405) {
      console.log('✅ Webhook 端点存在 (Method Not Allowed 是正常的)');
    } else {
      console.log('⚠️  意外的响应状态');
    }
    
  } catch (error) {
    console.error('❌ 无法访问 webhook 端点:', error.message);
  }
}

// 运行测试
async function main() {
  await testWebhookEndpoint();
  await testWebhook();
}

main();
