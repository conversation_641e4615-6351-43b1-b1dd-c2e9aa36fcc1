import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
export const runtime = "edge";

export async function POST(req: NextRequest) {
  try {
    const {
      id,
      agentName,
      description,
      targetAudience,
      features,
      faq,
      systemPrompt,
      imageUrl,
      modelId,
    } = await req.json();
    
    // 验证必要字段
    if (!id || !agentName || !description || !systemPrompt || !modelId) {
      return NextResponse.json(
        { error: "缺少必要字段" },
        { status: 400 }
      );
    }
    
    // 更新数据库中的智能体信息
    const { data, error } = await supabase
      .from("agents")
      .update({
        name: agentName,
        description,
        target_audience: targetAudience || null,
        features: features || null,
        faq: faq || null,
        system_prompt: systemPrompt,
        image_url: imageUrl || null,
        model_id: modelId,
      })
      .eq("id", id)
      .select("id, slug")
      .single();
    
    if (error) {
      console.error("更新智能体失败:", error);
      return NextResponse.json(
        { error: "更新智能体失败" },
        { status: 500 }
      );
    }
    
    // 返回成功结果
    return NextResponse.json({
      message: "智能体已成功更新",
      slug: data.slug,
      id: data.id,
    });
    
  } catch (error) {
    console.error("处理请求时出错:", error);
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    );
  }
} 